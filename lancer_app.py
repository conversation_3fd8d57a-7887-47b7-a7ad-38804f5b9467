#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de lancement simple de l'application
==========================================
"""

import os
from congratulations_poster import CongratulationsPoster

def main():
    print("🎓 LANCEMENT DE L'APPLICATION")
    print("=" * 40)
    
    # Vérifier que les dossiers existent
    if not os.path.exists('templates/fond_defaut.png'):
        print("❌ Template non trouvé")
        return
    
    if not os.path.exists('data/exemple_avec_photos.csv'):
        print("❌ Fichier CSV non trouvé")
        return
    
    # C<PERSON>er le générateur
    print("📝 Initialisation du générateur...")
    generator = CongratulationsPoster('templates/fond_defaut.png')
    
    # Générer une affiche
    print("🎨 Génération de l'affiche...")
    output_path = generator.generate_poster(
        'data/exemple_avec_photos.csv', 
        'Démonstration Application'
    )
    
    if output_path:
        print(f"✅ Succès ! Affiche créée: {output_path}")
        
        # Vérifier la taille du fichier
        if os.path.exists(output_path):
            size_kb = os.path.getsize(output_path) / 1024
            print(f"📊 Taille: {size_kb:.1f} KB")
    else:
        print("❌ Échec de la génération")

if __name__ == "__main__":
    main()
