#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de l'interface graphique professionnelle
"""

import os
import sys

def test_interface_pro():
    """Teste l'interface professionnelle."""
    print("🎓 Test de l'Interface Professionnelle")
    print("=" * 50)
    
    # Vérifier les dépendances
    try:
        import tkinter as tk
        from tkinter import ttk
        print("✅ Tkinter disponible")
    except ImportError:
        print("❌ Tkinter non disponible")
        return False
    
    try:
        from PIL import Image, ImageTk
        print("✅ Pillow disponible")
    except ImportError:
        print("❌ Pillow non disponible")
        return False
    
    try:
        import pandas as pd
        print("✅ Pandas disponible")
    except ImportError:
        print("❌ Pandas non disponible")
        return False
    
    # Vérifier les fichiers
    required_files = [
        'interface_gui_pro.py',
        'congratulations_poster.py'
    ]
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file} trouvé")
        else:
            print(f"❌ {file} manquant")
            return False
    
    # Créer des données de test si nécessaire
    if not os.path.exists('data'):
        os.makedirs('data')
    
    if not os.path.exists('data/test_ameliorations_completes.csv'):
        print("📊 Création de données de test...")
        create_test_data()
    
    print("\n🚀 Lancement de l'interface professionnelle...")
    
    try:
        # Importer et lancer l'interface
        from interface_gui_pro import ModernPosterApp
        
        app = ModernPosterApp()
        print("✅ Interface initialisée avec succès")
        
        # Afficher les fonctionnalités
        print("\n🎨 Fonctionnalités disponibles:")
        print("   📁 Onglet Fichiers - Gestion des fichiers CSV, templates, images")
        print("   📝 Onglet Contenu - Nom de classe, école, année")
        print("   📐 Onglet Layout - Dimensions, orientation, espacement")
        print("   🎨 Onglet Style - Couleurs, polices")
        print("   ⚙️ Onglet Avancé - Options et espacement avancé")
        print("   👁️ Aperçu en temps réel avec zoom")
        print("   🎨 Personnalisation rapide intégrée")
        print("   💾 Export haute qualité")
        
        print("\n💡 Utilisation:")
        print("   1. Sélectionnez un fichier CSV dans l'onglet Fichiers")
        print("   2. Personnalisez les couleurs et la mise en page")
        print("   3. Générez l'aperçu en temps réel")
        print("   4. Utilisez le zoom pour examiner les détails")
        print("   5. Exportez votre affiche")
        
        print("\n🎉 Interface prête ! Fermer cette fenêtre pour continuer...")
        
        # Lancer l'interface
        app.run()
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du lancement: {e}")
        return False

def create_test_data():
    """Crée des données de test."""
    import pandas as pd
    
    # Données d'exemple
    sample_data = {
        'Nom': [
            'Alice Martin', 'Bob Dupont', 'Claire Rousseau', 'David Moreau',
            'Emma Bernard', 'Felix Dubois', 'Grace Leroy', 'Hugo Petit',
            'Iris Blanc', 'Jules Noir', 'Karine Vert', 'Louis Rouge'
        ],
        'Note': [18.5, 17.2, 16.8, 16.5, 15.9, 15.2, 14.8, 14.5, 14.2, 13.8, 13.5, 13.2],
        'Classement': list(range(1, 13)),
        'Mention': [
            'Très Bien', 'Très Bien', 'Bien', 'Bien',
            'Bien', 'Assez Bien', 'Assez Bien', 'Assez Bien',
            'Assez Bien', 'Assez Bien', 'Assez Bien', 'Assez Bien'
        ],
        'Photo': [f'photos/eleve_{i+1}.jpg' for i in range(12)]
    }
    
    # Sauvegarder le CSV
    df = pd.DataFrame(sample_data)
    df.to_csv('data/test_ameliorations_completes.csv', index=False, encoding='utf-8')
    print("✅ Données de test créées")

def show_features():
    """Affiche les fonctionnalités de l'interface professionnelle."""
    print("\n🎨 Interface Professionnelle - Fonctionnalités")
    print("=" * 60)
    
    features = {
        "🎯 Interface Moderne": [
            "Design professionnel avec thème moderne",
            "Onglets organisés pour une navigation intuitive",
            "Barre d'outils avec contrôles de zoom",
            "Barre d'état avec progression en temps réel"
        ],
        "📁 Gestion des Fichiers": [
            "Sélection de fichiers CSV avec analyse automatique",
            "Support des templates personnalisés",
            "Images d'en-tête et de pied de page",
            "Dossier de sortie configurable"
        ],
        "🎨 Personnalisation Avancée": [
            "Sélecteur de couleurs intégré",
            "Contrôle des dimensions en temps réel",
            "Options de mise en page flexibles",
            "Configuration des polices et tailles"
        ],
        "👁️ Aperçu en Temps Réel": [
            "Génération d'aperçu instantanée",
            "Zoom avant/arrière avec molette",
            "Ajustement automatique à la fenêtre",
            "Défilement fluide pour grandes affiches"
        ],
        "⚙️ Options Avancées": [
            "Contrôle de l'espacement du podium",
            "Ajustement de l'espacement horizontal/vertical",
            "Options d'affichage (numéros, décorations)",
            "Hauteur automatique ou manuelle"
        ],
        "💾 Export Professionnel": [
            "Export haute résolution PNG/JPEG",
            "Sauvegarde des paramètres",
            "Gestion des erreurs robuste",
            "Feedback utilisateur complet"
        ]
    }
    
    for category, items in features.items():
        print(f"\n{category}")
        print("-" * 40)
        for item in items:
            print(f"   • {item}")
    
    print(f"\n🚀 Avantages de l'Interface Professionnelle:")
    print(f"   ✅ Workflow optimisé pour la productivité")
    print(f"   ✅ Personnalisation complète sans programmation")
    print(f"   ✅ Aperçu en temps réel pour validation immédiate")
    print(f"   ✅ Interface intuitive pour tous les utilisateurs")
    print(f"   ✅ Qualité professionnelle des exports")

def main():
    """Fonction principale."""
    print("🎓 Test de l'Interface Graphique Professionnelle")
    print("=" * 60)
    print("Cette interface moderne offre une expérience utilisateur")
    print("professionnelle avec personnalisation avancée et aperçu en temps réel.")
    print()
    
    # Afficher les fonctionnalités
    show_features()
    
    print(f"\n" + "=" * 60)
    input("Appuyez sur Entrée pour lancer l'interface professionnelle...")
    
    # Tester l'interface
    success = test_interface_pro()
    
    if success:
        print("\n🎉 Test réussi ! L'interface professionnelle fonctionne parfaitement.")
    else:
        print("\n❌ Problème détecté. Vérifiez les dépendances et les fichiers.")

if __name__ == "__main__":
    main()
