#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Démonstration de l'interface graphique
=====================================

Script pour présenter les fonctionnalités de l'interface graphique.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import webbrowser
from pathlib import Path


class DemoInterface:
    """Interface de démonstration."""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎓 Démonstration - Interface Graphique")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f8ff')
        
        self.setup_ui()
    
    def setup_ui(self):
        """Configure l'interface de démonstration."""
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Titre
        title_label = tk.Label(
            main_frame,
            text="🎓 Interface Graphique\nGénérateur d'Affiches de Félicitations",
            font=('Arial', 18, 'bold'),
            bg='#f0f8ff',
            fg='#2c3e50'
        )
        title_label.pack(pady=(0, 30))
        
        # Description
        desc_text = (
            "Interface Windows complète avec aperçu en temps réel\n"
            "pour créer facilement des affiches de félicitations professionnelles."
        )
        desc_label = tk.Label(
            main_frame,
            text=desc_text,
            font=('Arial', 12),
            bg='#f0f8ff',
            fg='#34495e'
        )
        desc_label.pack(pady=(0, 30))
        
        # Fonctionnalités
        features_frame = ttk.LabelFrame(main_frame, text="✨ Fonctionnalités Principales", padding="15")
        features_frame.pack(fill=tk.X, pady=(0, 20))
        
        features = [
            "👁️ Aperçu en temps réel avant export",
            "📊 Analyse automatique des fichiers CSV",
            "🎨 Configuration visuelle avancée",
            "💾 Export multi-formats (PNG, JPEG, BMP)",
            "⚙️ Presets pour différentes résolutions",
            "🔄 Interface intuitive et responsive"
        ]
        
        for feature in features:
            feature_label = tk.Label(
                features_frame,
                text=feature,
                font=('Arial', 11),
                bg='#f0f8ff',
                fg='#2c3e50',
                anchor='w'
            )
            feature_label.pack(fill=tk.X, pady=2)
        
        # Boutons d'action
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=20)
        
        # Bouton principal
        launch_btn = tk.Button(
            buttons_frame,
            text="🚀 Lancer l'Interface Graphique",
            font=('Arial', 14, 'bold'),
            bg='#3498db',
            fg='white',
            relief='raised',
            bd=3,
            padx=20,
            pady=10,
            command=self.launch_interface
        )
        launch_btn.pack(pady=(0, 15))
        
        # Boutons secondaires
        secondary_frame = ttk.Frame(buttons_frame)
        secondary_frame.pack()
        
        ttk.Button(
            secondary_frame,
            text="📖 Documentation",
            command=self.open_documentation
        ).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(
            secondary_frame,
            text="📁 Ouvrir Dossier",
            command=self.open_folder
        ).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(
            secondary_frame,
            text="🧪 Tests",
            command=self.run_tests
        ).pack(side=tk.LEFT)
        
        # Informations système
        info_frame = ttk.LabelFrame(main_frame, text="ℹ️ Informations", padding="10")
        info_frame.pack(fill=tk.X, pady=(20, 0))
        
        self.check_system_info(info_frame)
    
    def check_system_info(self, parent):
        """Affiche les informations système."""
        info_text = tk.Text(parent, height=8, width=70, font=('Consolas', 9))
        info_text.pack(fill=tk.BOTH, expand=True)
        
        # Vérifier les dépendances
        info_text.insert(tk.END, "🔍 Vérification du système...\n\n")
        
        # Python
        import sys
        info_text.insert(tk.END, f"🐍 Python: {sys.version.split()[0]}\n")
        
        # Modules
        modules_status = []
        
        try:
            import tkinter
            modules_status.append("✅ tkinter: Disponible")
        except ImportError:
            modules_status.append("❌ tkinter: Manquant")
        
        try:
            import pandas
            modules_status.append(f"✅ pandas: {pandas.__version__}")
        except ImportError:
            modules_status.append("❌ pandas: Manquant")
        
        try:
            from PIL import Image
            modules_status.append(f"✅ Pillow: Disponible")
        except ImportError:
            modules_status.append("❌ Pillow: Manquant")
        
        # Fichiers
        files_status = []
        required_files = [
            "interface_gui.py",
            "congratulations_poster.py",
            "config.py"
        ]
        
        for file in required_files:
            if os.path.exists(file):
                files_status.append(f"✅ {file}")
            else:
                files_status.append(f"❌ {file}")
        
        # Dossiers
        folders_status = []
        required_folders = ["data", "photos", "templates", "output"]
        
        for folder in required_folders:
            if os.path.exists(folder):
                count = len([f for f in os.listdir(folder) if os.path.isfile(os.path.join(folder, f))])
                folders_status.append(f"📁 {folder}/: {count} fichier(s)")
            else:
                folders_status.append(f"❌ {folder}/: Manquant")
        
        # Afficher les résultats
        info_text.insert(tk.END, "\n📦 Modules:\n")
        for status in modules_status:
            info_text.insert(tk.END, f"   {status}\n")
        
        info_text.insert(tk.END, "\n📄 Fichiers:\n")
        for status in files_status:
            info_text.insert(tk.END, f"   {status}\n")
        
        info_text.insert(tk.END, "\n📂 Dossiers:\n")
        for status in folders_status:
            info_text.insert(tk.END, f"   {status}\n")
        
        # Statut global
        all_modules_ok = all("✅" in status for status in modules_status)
        all_files_ok = all("✅" in status for status in files_status)
        
        if all_modules_ok and all_files_ok:
            info_text.insert(tk.END, "\n🎉 Système prêt pour l'interface graphique!")
        else:
            info_text.insert(tk.END, "\n⚠️  Certains éléments sont manquants.")
            info_text.insert(tk.END, "\n💡 Exécutez 'python setup.py' pour installer.")
        
        info_text.config(state=tk.DISABLED)
    
    def launch_interface(self):
        """Lance l'interface graphique principale."""
        try:
            # Vérifier que le fichier existe
            if not os.path.exists("interface_gui.py"):
                messagebox.showerror(
                    "Erreur",
                    "Le fichier interface_gui.py n'a pas été trouvé.\n\n"
                    "Assurez-vous d'être dans le bon dossier."
                )
                return
            
            # Fermer la fenêtre de démo
            self.root.destroy()
            
            # Lancer l'interface principale
            from interface_gui import main as gui_main
            gui_main()
            
        except ImportError as e:
            messagebox.showerror(
                "Erreur d'importation",
                f"Impossible d'importer l'interface graphique:\n{str(e)}\n\n"
                "Vérifiez que tous les modules sont installés."
            )
        except Exception as e:
            messagebox.showerror(
                "Erreur",
                f"Erreur lors du lancement:\n{str(e)}"
            )
    
    def open_documentation(self):
        """Ouvre la documentation."""
        docs = [
            "README_INTERFACE.md",
            "README.md",
            "GUIDE_CSV.md"
        ]
        
        for doc in docs:
            if os.path.exists(doc):
                try:
                    # Essayer d'ouvrir avec l'application par défaut
                    os.startfile(doc)
                    break
                except:
                    # Fallback: ouvrir dans le navigateur
                    webbrowser.open(f"file://{os.path.abspath(doc)}")
                    break
        else:
            messagebox.showinfo(
                "Documentation",
                "Documentation disponible dans les fichiers:\n"
                "- README_INTERFACE.md\n"
                "- README.md\n"
                "- GUIDE_CSV.md"
            )
    
    def open_folder(self):
        """Ouvre le dossier du projet."""
        try:
            os.startfile(".")
        except:
            messagebox.showinfo(
                "Dossier",
                f"Dossier du projet:\n{os.path.abspath('.')}"
            )
    
    def run_tests(self):
        """Lance les tests."""
        test_files = [
            "test_poster_generator.py",
            "test_csv_files.py"
        ]
        
        available_tests = [f for f in test_files if os.path.exists(f)]
        
        if available_tests:
            messagebox.showinfo(
                "Tests",
                f"Tests disponibles:\n" + "\n".join(f"- {f}" for f in available_tests) +
                f"\n\nPour les exécuter:\npython {available_tests[0]}"
            )
        else:
            messagebox.showwarning(
                "Tests",
                "Aucun fichier de test trouvé."
            )
    
    def run(self):
        """Lance l'interface de démonstration."""
        self.root.mainloop()


def main():
    """Fonction principale."""
    print("🎓 Démonstration de l'Interface Graphique")
    print("=" * 45)
    
    try:
        demo = DemoInterface()
        demo.run()
    except Exception as e:
        print(f"❌ Erreur: {e}")
        input("Appuyez sur Entrée pour quitter...")


if __name__ == "__main__":
    main()
