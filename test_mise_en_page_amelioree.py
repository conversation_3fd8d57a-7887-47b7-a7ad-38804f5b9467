#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test des améliorations de mise en page du générateur d'affiches
"""

import os
import pandas as pd
from congratulations_poster import Congratulations<PERSON>oster

def create_test_data_various_sizes():
    """Crée des jeux de données de différentes tailles pour tester la mise en page."""
    
    # Noms d'élèves
    first_names = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', 
                   '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
                   '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
                   '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>']
    
    last_names = ['<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>',
                  '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>',
                  '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Turquoise', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Indigo']
    
    # C<PERSON>er le dossier data s'il n'existe pas
    if not os.path.exists('data'):
        os.makedirs('data')
    
    test_cases = [
        {'size': 5, 'name': 'Petit Groupe'},
        {'size': 12, 'name': 'Groupe Moyen'},
        {'size': 18, 'name': 'Grande Classe'},
        {'size': 25, 'name': 'Très Grande Classe'}
    ]
    
    created_files = []
    
    for case in test_cases:
        num_students = case['size']
        case_name = case['name']
        
        names = []
        notes = []
        mentions = []
        
        for i in range(num_students):
            # Nom
            first = first_names[i % len(first_names)]
            last = last_names[i % len(last_names)]
            names.append(f"{first} {last}")
            
            # Note décroissante avec variation
            base_note = 20.0 - (i * 0.3)
            variation = (-1) ** i * 0.15
            note = max(10.0, base_note + variation)
            notes.append(round(note, 2))
            
            # Mention selon la note
            if note >= 16:
                mentions.append('Très Bien')
            elif note >= 14:
                mentions.append('Bien')
            elif note >= 12:
                mentions.append('Assez Bien')
            else:
                mentions.append('Passable')
        
        sample_data = {
            'Nom': names,
            'Note': notes,
            'Classement': list(range(1, num_students + 1)),
            'Mention': mentions,
            'Photo': [f'photos/eleve_{i+1}.jpg' for i in range(num_students)]
        }
        
        # Sauvegarder le CSV
        df = pd.DataFrame(sample_data)
        filename = f'data/test_mise_en_page_{num_students}_eleves.csv'
        df.to_csv(filename, index=False, encoding='utf-8')
        
        created_files.append({
            'file': filename,
            'size': num_students,
            'name': case_name
        })
        
        print(f"✅ Créé: {filename} ({num_students} élèves)")
    
    return created_files

def test_layout_improvements():
    """Teste toutes les améliorations de mise en page."""
    print("🎨 Test des Améliorations de Mise en Page")
    print("=" * 50)
    print("Nouvelles fonctionnalités testées :")
    print("✅ Configuration avancée de mise en page")
    print("✅ Disposition adaptative selon le nombre d'élèves")
    print("✅ Titre amélioré avec décorations")
    print("✅ Texte des élèves optimisé")
    print("✅ Espacement intelligent")
    print()
    
    # Créer les données de test
    test_files = create_test_data_various_sizes()
    
    # Initialiser le générateur avec les améliorations
    generator = CongratulationsPoster(
        template_path='templates/fond_defaut.png',
        output_dir='output',
        header_image='templates/header_exemple.png' if os.path.exists('templates/header_exemple.png') else None,
        footer_image='templates/footer_exemple.png' if os.path.exists('templates/footer_exemple.png') else None
    )
    
    print(f"📊 Configuration de mise en page:")
    print(f"   - Espacement podium: {generator.layout_config['podium_spacing']}px")
    print(f"   - Espacement régulier X: {generator.layout_config['regular_spacing_x']}px")
    print(f"   - Espacement régulier Y: {generator.layout_config['regular_spacing_y']}px")
    print(f"   - Marge titre: {generator.layout_config['title_margin']}px")
    print(f"   - Espacement photo-texte: {generator.layout_config['photo_text_spacing']}px")
    print()
    
    results = []
    
    for test_case in test_files:
        file_path = test_case['file']
        num_students = test_case['size']
        case_name = test_case['name']
        
        print(f"🧪 Test: {case_name} ({num_students} élèves)")
        
        # Charger les données
        df = pd.read_csv(file_path)
        
        # Calculer la disposition optimale
        optimal_per_row = generator.get_optimal_students_per_row(num_students - 3)  # -3 pour le podium
        calculated_height = generator.calculate_dynamic_height(num_students)
        
        print(f"   📐 Disposition calculée:")
        print(f"      - Podium: 3 élèves (2ème-1er-3ème)")
        print(f"      - Autres: {num_students - 3} élèves, {optimal_per_row} par ligne")
        print(f"      - Hauteur: {calculated_height}px")
        
        # Générer l'affiche
        class_name = f"{case_name} - Test Layout"
        output_path = generator.generate_poster(file_path, class_name)
        
        if output_path:
            # Calculer la taille du fichier
            file_size = os.path.getsize(output_path) / (1024 * 1024)  # MB
            
            result = {
                'case': case_name,
                'students': num_students,
                'per_row': optimal_per_row,
                'height': calculated_height,
                'file': output_path,
                'size_mb': file_size
            }
            results.append(result)
            
            print(f"   ✅ Généré: {output_path}")
            print(f"   💾 Taille: {file_size:.2f} MB")
        else:
            print(f"   ❌ Erreur lors de la génération")
        
        print()
    
    # Résumé des résultats
    print(f"📈 Résumé des Tests de Mise en Page")
    print(f"=" * 40)
    print(f"{'Cas':<20} {'Élèves':<8} {'Par ligne':<10} {'Hauteur':<8} {'Taille':<8}")
    print("-" * 60)
    
    for result in results:
        print(f"{result['case']:<20} {result['students']:<8} {result['per_row']:<10} "
              f"{result['height']:<8} {result['size_mb']:.1f}MB")
    
    print(f"\n🎯 Améliorations Validées:")
    print(f"   ✅ Disposition adaptative selon le nombre")
    print(f"   ✅ Hauteur dynamique optimisée")
    print(f"   ✅ Espacement intelligent et configurable")
    print(f"   ✅ Titre avec décorations et style amélioré")
    print(f"   ✅ Texte des élèves compact et lisible")
    print(f"   ✅ Podium avec effet escalier prononcé")
    
    return results

def test_layout_configuration():
    """Teste la configuration personnalisée de mise en page."""
    print(f"\n🔧 Test de Configuration Personnalisée")
    print("=" * 40)
    
    # Configuration personnalisée
    custom_generator = CongratulationsPoster('templates/fond_defaut.png')
    
    # Modifier la configuration
    custom_generator.layout_config.update({
        'podium_spacing': 400,      # Plus d'espace pour le podium
        'podium_height_diff': 60,   # Effet escalier plus prononcé
        'regular_spacing_x': 200,   # Plus d'espace horizontal
        'regular_spacing_y': 220,   # Plus d'espace vertical
        'title_margin': 100,        # Plus de marge pour le titre
    })
    
    print(f"📊 Configuration personnalisée appliquée:")
    for key, value in custom_generator.layout_config.items():
        print(f"   - {key}: {value}")
    
    # Test avec un groupe moyen
    test_file = 'data/test_mise_en_page_12_eleves.csv'
    if os.path.exists(test_file):
        output_path = custom_generator.generate_poster(test_file, "Configuration Personnalisée")
        if output_path:
            print(f"✅ Affiche avec configuration personnalisée: {output_path}")
        else:
            print(f"❌ Erreur avec configuration personnalisée")
    
    print(f"\n💡 La configuration de mise en page est entièrement personnalisable!")

def compare_layouts():
    """Compare les différentes dispositions."""
    print(f"\n📊 Comparaison des Dispositions")
    print("=" * 35)
    
    test_cases = [5, 12, 18, 25]
    
    print(f"{'Élèves':<8} {'Par ligne':<10} {'Lignes':<8} {'Disposition':<15}")
    print("-" * 50)
    
    generator = CongratulationsPoster('templates/fond_defaut.png')
    
    for num_students in test_cases:
        remaining = num_students - 3  # Après le podium
        per_row = generator.get_optimal_students_per_row(remaining)
        lines = (remaining + per_row - 1) // per_row if remaining > 0 else 0
        
        if num_students <= 5:
            disposition = "Compact"
        elif num_students <= 12:
            disposition = "Équilibré"
        elif num_students <= 21:
            disposition = "Organisé"
        else:
            disposition = "Dense"
        
        print(f"{num_students:<8} {per_row:<10} {lines:<8} {disposition:<15}")
    
    print(f"\n🎯 Logique de Disposition:")
    print(f"   • ≤5 élèves: Une ligne (compact)")
    print(f"   • 6-12 élèves: 6 par ligne (équilibré)")
    print(f"   • 13-21 élèves: 7 par ligne (organisé)")
    print(f"   • 22-32 élèves: 8 par ligne (dense)")
    print(f"   • >32 élèves: 10 par ligne (très dense)")

def main():
    """Fonction principale de test."""
    print("🎓 Test des Améliorations de Mise en Page")
    print("=" * 60)
    print("Ce script teste toutes les améliorations de mise en page :")
    print("• Configuration avancée et personnalisable")
    print("• Disposition adaptative selon le nombre d'élèves")
    print("• Titre amélioré avec décorations")
    print("• Texte optimisé pour les élèves")
    print("• Espacement intelligent")
    print()
    
    # Tests principaux
    results = test_layout_improvements()
    
    # Test de configuration
    test_layout_configuration()
    
    # Comparaison des dispositions
    compare_layouts()
    
    print(f"\n🎉 Tous les tests de mise en page réussis!")
    print(f"📁 Affiches générées dans le dossier 'output/'")
    print(f"🎨 Utilisez l'interface graphique pour voir les améliorations en action")

if __name__ == "__main__":
    main()
