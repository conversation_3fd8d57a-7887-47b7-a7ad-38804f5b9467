# 🖥️ Interface Graphique Windows - Générateur d'Affiches

## 🎯 Vue d'Ensemble

L'interface graphique Windows offre une expérience utilisateur intuitive pour créer des affiches de félicitations avec **aperçu en temps réel** avant l'export.

## 🚀 Lancement Rapide

### Méthode Simple
```bash
python lancer_interface.py
```

### Méthode Directe
```bash
python interface_gui.py
```

## 🖼️ Fonctionnalités de l'Interface

### 📋 Panel de Configuration (Gauche)
- **📊 Sélection du fichier CSV** avec bouton parcourir
- **🎨 Choix du template** d'arrière-plan
- **📚 Nom de la classe** personnalisable
- **📁 Dossier de sortie** configurable
- **📈 Informations CSV** en temps réel :
  - Nombre d'élèves
  - Statistiques des notes
  - Répartition des mentions
  - Aperçu des données

### 👁️ Panel d'Aperçu (Droite)
- **🔄 Génération d'aperçu** en temps réel
- **🖼️ Affichage de l'affiche** avec zoom/défilement
- **📐 Dimensions réelles** préservées
- **⚡ Mise à jour instantanée** lors des modifications

### 🎛️ Contrôles (Bas)
- **📊 Charger CSV** : Analyse le fichier sélectionné
- **🎨 Générer Aperçu** : Crée l'aperçu de l'affiche
- **💾 Exporter Image** : Sauvegarde l'affiche finale
- **⚙️ Configuration** : Paramètres avancés

## ⚙️ Configuration Avancée

### 📐 Onglet Dimensions
- **📏 Taille de l'affiche** : Largeur × Hauteur
- **📸 Taille des photos** : Diamètre des cercles
- **👥 Élèves par ligne** : Disposition automatique
- **📏 Marges** : Espacement autour de l'affiche

#### 📋 Presets Disponibles
- **HD (1920×1080)** : Format standard écran
- **4K (3840×2160)** : Haute résolution
- **A4 (2480×3508)** : Format impression

### 🎨 Onglet Couleurs
- **🥇 Très Bien** : Couleur or
- **🥈 Bien** : Couleur argent
- **🥉 Assez Bien** : Couleur bronze
- **📝 Passable** : Couleur grise

### 🔤 Onglet Polices
- **📏 Tailles configurables** :
  - Titre principal
  - Noms des élèves
  - Informations (notes, mentions)

## 🔄 Workflow Recommandé

### 1. Préparation
1. **📊 Préparez votre fichier CSV** selon le format requis
2. **📸 Placez les photos** dans le dossier `photos/`
3. **🎨 Choisissez un template** (optionnel)

### 2. Configuration
1. **🚀 Lancez l'interface** : `python lancer_interface.py`
2. **📂 Sélectionnez votre CSV** avec le bouton "Parcourir"
3. **📚 Entrez le nom de la classe**
4. **📁 Choisissez le dossier de sortie**

### 3. Aperçu
1. **📊 Cliquez "Charger CSV"** pour voir les statistiques
2. **🎨 Cliquez "Générer Aperçu"** pour voir l'affiche
3. **🔍 Vérifiez le résultat** dans le panel d'aperçu
4. **⚙️ Ajustez la configuration** si nécessaire

### 4. Export
1. **💾 Cliquez "Exporter Image"** quand satisfait
2. **📁 Choisissez l'emplacement** et le nom du fichier
3. **✅ Confirmez l'export** - L'affiche est sauvegardée !

## 🎨 Personnalisation Visuelle

### Templates Personnalisés
- Placez vos images de fond dans `templates/`
- Formats supportés : PNG, JPG, BMP
- Résolution recommandée : 1920×1080 ou plus

### Photos des Élèves
- **Formats supportés** : JPG, PNG, BMP, GIF
- **Résolution recommandée** : 200×200 pixels minimum
- **Photos manquantes** : Remplacées automatiquement par des placeholders

## 🔧 Dépannage

### Interface ne se Lance Pas
```bash
# Vérifier les dépendances
python -c "import tkinter, pandas; from PIL import Image"

# Réinstaller si nécessaire
pip install -r requirements.txt
```

### Erreur "Module not found"
```bash
# Installation complète
python setup.py
```

### Aperçu ne s'Affiche Pas
1. **Vérifiez le fichier CSV** : Format correct ?
2. **Vérifiez le template** : Fichier existant ?
3. **Consultez les messages d'erreur** dans l'interface

### Export Échoue
1. **Générez d'abord un aperçu** avec succès
2. **Vérifiez les permissions** du dossier de sortie
3. **Choisissez un nom de fichier valide**

## 💡 Conseils d'Utilisation

### Performance
- **Grandes classes (20+ élèves)** : L'aperçu peut prendre quelques secondes
- **Photos haute résolution** : Peuvent ralentir la génération
- **Fermez l'aperçu** avant de générer une nouvelle affiche

### Qualité
- **Utilisez des photos de bonne qualité** pour un meilleur rendu
- **Testez différents templates** pour trouver le style optimal
- **Ajustez la taille des photos** selon le nombre d'élèves

### Productivité
- **Sauvegardez vos configurations** préférées
- **Utilisez les presets** pour des formats standards
- **Préparez plusieurs CSV** pour traiter plusieurs classes

## 🎯 Fonctionnalités Avancées

### Aperçu en Temps Réel
- **Zoom automatique** pour s'adapter à la fenêtre
- **Défilement** pour les grandes affiches
- **Mise à jour instantanée** lors des changements

### Gestion d'Erreurs
- **Messages informatifs** pour guider l'utilisateur
- **Validation automatique** des fichiers CSV
- **Récupération gracieuse** en cas d'erreur

### Multi-formats
- **Export PNG** : Qualité maximale
- **Export JPEG** : Taille réduite
- **Export BMP** : Compatibilité maximale

## 📊 Statistiques Affichées

L'interface affiche automatiquement :
- **👥 Nombre d'élèves** dans la classe
- **📊 Note moyenne** de la classe
- **🏆 Meilleure note** obtenue
- **📉 Note la plus basse**
- **🏅 Répartition des mentions** par catégorie
- **📋 Aperçu des premières données**

## 🔄 Mises à Jour

L'interface se met automatiquement à jour quand vous :
- **Changez de fichier CSV**
- **Modifiez le nom de la classe**
- **Sélectionnez un nouveau template**
- **Ajustez la configuration**

---

**🎓 Profitez de votre interface graphique pour créer facilement de magnifiques affiches de félicitations ! 🎓**
