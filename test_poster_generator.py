#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tests pour le générateur d'affiches de félicitations
===================================================

Ce script teste les différentes fonctionnalités du générateur d'affiches.
"""

import os
import unittest
import pandas as pd
from PIL import Image
from congratulations_poster import CongratulationsPoster


class TestCongratulationsPoster(unittest.TestCase):
    """
    Tests unitaires pour la classe CongratulationsPoster.
    """
    
    def setUp(self):
        """Configuration avant chaque test."""
        self.test_template = 'test_template.png'
        self.test_output_dir = 'test_output'
        self.test_csv = 'test_data.csv'
        
        # Créer un template de test
        test_img = Image.new('RGB', (800, 600), (255, 255, 255))
        test_img.save(self.test_template)
        
        # Créer des données de test
        test_data = {
            'Nom': ['Test Student 1', 'Test Student 2'],
            'Note': [18.0, 16.5],
            'Classement': [1, 2],
            'Mention': ['Très Bien', 'Bien'],
            'Photo': ['nonexistent1.jpg', 'nonexistent2.jpg']
        }
        df = pd.DataFrame(test_data)
        df.to_csv(self.test_csv, index=False)
        
        # Initialiser le générateur
        self.generator = CongratulationsPoster(
            template_path=self.test_template,
            output_dir=self.test_output_dir
        )
    
    def tearDown(self):
        """Nettoyage après chaque test."""
        # Supprimer les fichiers de test
        for file_path in [self.test_template, self.test_csv]:
            if os.path.exists(file_path):
                os.remove(file_path)
        
        # Supprimer le dossier de sortie de test
        if os.path.exists(self.test_output_dir):
            for file in os.listdir(self.test_output_dir):
                os.remove(os.path.join(self.test_output_dir, file))
            os.rmdir(self.test_output_dir)
    
    def test_load_students_data(self):
        """Test du chargement des données d'élèves."""
        df = self.generator.load_students_data(self.test_csv)
        
        self.assertFalse(df.empty)
        self.assertEqual(len(df), 2)
        self.assertIn('Nom', df.columns)
        self.assertIn('Note', df.columns)
        self.assertIn('Classement', df.columns)
        self.assertIn('Mention', df.columns)
        self.assertIn('Photo', df.columns)
    
    def test_create_circular_photo(self):
        """Test de la création de photos circulaires."""
        # Test avec une photo inexistante (doit retourner une photo par défaut)
        photo = self.generator.create_circular_photo('nonexistent.jpg', 100)
        
        self.assertIsNotNone(photo)
        self.assertEqual(photo.size, (100, 100))
    
    def test_create_default_photo(self):
        """Test de la création de photo par défaut."""
        photo = self.generator.create_default_photo(120)
        
        self.assertIsNotNone(photo)
        self.assertEqual(photo.size, (120, 120))
    
    def test_get_mention_color(self):
        """Test de l'attribution des couleurs selon les mentions."""
        self.assertEqual(
            self.generator.get_mention_color('Très Bien'),
            self.generator.colors['gold']
        )
        self.assertEqual(
            self.generator.get_mention_color('Bien'),
            self.generator.colors['silver']
        )
        self.assertEqual(
            self.generator.get_mention_color('Assez Bien'),
            self.generator.colors['bronze']
        )
    
    def test_calculate_positions(self):
        """Test du calcul des positions des élèves."""
        positions = self.generator.calculate_positions(6)
        
        self.assertEqual(len(positions), 6)
        # Vérifier que toutes les positions sont des tuples (x, y)
        for pos in positions:
            self.assertIsInstance(pos, tuple)
            self.assertEqual(len(pos), 2)
            self.assertIsInstance(pos[0], int)
            self.assertIsInstance(pos[1], int)
    
    def test_generate_poster(self):
        """Test de la génération complète d'affiche."""
        output_path = self.generator.generate_poster(self.test_csv, "Test Class")
        
        self.assertTrue(os.path.exists(output_path))
        self.assertTrue(output_path.endswith('.png'))
        
        # Vérifier que l'image générée est valide
        img = Image.open(output_path)
        self.assertIsNotNone(img)
        self.assertEqual(img.size, (self.generator.poster_width, self.generator.poster_height))


def run_performance_test():
    """
    Test de performance avec un grand nombre d'élèves.
    """
    print("\n=== Test de performance ===")
    
    # Créer des données avec beaucoup d'élèves
    num_students = 20
    large_data = {
        'Nom': [f'Élève {i+1}' for i in range(num_students)],
        'Note': [15.0 + (i % 5) for i in range(num_students)],
        'Classement': list(range(1, num_students + 1)),
        'Mention': ['Bien'] * num_students,
        'Photo': [f'photo_{i+1}.jpg' for i in range(num_students)]
    }
    
    df = pd.DataFrame(large_data)
    csv_path = 'large_test_data.csv'
    df.to_csv(csv_path, index=False)
    
    # Créer un template de test
    template_path = 'large_test_template.png'
    test_img = Image.new('RGB', (1920, 1080), (240, 248, 255))
    test_img.save(template_path)
    
    try:
        # Tester la génération
        generator = CongratulationsPoster(template_path, 'large_test_output')
        
        import time
        start_time = time.time()
        output_path = generator.generate_poster(csv_path, "Grande Classe")
        end_time = time.time()
        
        print(f"✅ Génération réussie pour {num_students} élèves")
        print(f"⏱️  Temps de génération: {end_time - start_time:.2f} secondes")
        print(f"📁 Fichier généré: {output_path}")
        
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / (1024 * 1024)  # MB
            print(f"📊 Taille du fichier: {file_size:.2f} MB")
    
    finally:
        # Nettoyage
        for file_path in [csv_path, template_path]:
            if os.path.exists(file_path):
                os.remove(file_path)
        
        if os.path.exists('large_test_output'):
            for file in os.listdir('large_test_output'):
                os.remove(os.path.join('large_test_output', file))
            os.rmdir('large_test_output')


def main():
    """
    Fonction principale pour exécuter tous les tests.
    """
    print("🧪 Tests du générateur d'affiches de félicitations")
    print("=" * 60)
    
    # Exécuter les tests unitaires
    print("1. Tests unitaires...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Exécuter le test de performance
    run_performance_test()
    
    print("\n" + "=" * 60)
    print("✅ Tous les tests terminés!")


if __name__ == "__main__":
    main()
