#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de l'interface améliorée
============================

Script pour tester les nouvelles fonctionnalités de l'interface.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import sys


def test_interface_components():
    """Teste les composants de l'interface améliorée."""
    print("🧪 Test des composants de l'interface améliorée")
    print("=" * 50)
    
    # Test d'importation
    try:
        from interface_gui import InterfaceGenerateurAffiches
        print("✅ Module interface_gui importé avec succès")
    except ImportError as e:
        print(f"❌ Erreur d'importation interface_gui: {e}")
        return False
    
    # Test du générateur amélioré
    try:
        from enhanced_poster_generator import EnhancedCongratulationsPoster
        print("✅ Module enhanced_poster_generator importé avec succès")
    except ImportError as e:
        print(f"❌ Erreur d'importation enhanced_poster_generator: {e}")
        return False
    
    # Test de création d'instance
    try:
        generator = EnhancedCongratulationsPoster()
        print("✅ Instance EnhancedCongratulationsPoster créée")
        
        # Test des nouvelles méthodes
        generator.set_orientation("portrait")
        print("✅ Méthode set_orientation testée")
        
        generator.set_dimensions(1080, 1920)
        print("✅ Méthode set_dimensions testée")
        
        generator.set_content(
            header_text="Test Header",
            footer_text="Test Footer",
            school_name="École Test"
        )
        print("✅ Méthode set_content testée")
        
        generator.set_style(
            background_color="#F0F8FF",
            title_color="#2C3E50",
            border_style="moderne"
        )
        print("✅ Méthode set_style testée")
        
    except Exception as e:
        print(f"❌ Erreur lors des tests: {e}")
        return False
    
    return True


def test_interface_gui():
    """Teste l'interface graphique améliorée."""
    print("\n🖥️ Test de l'interface graphique")
    print("-" * 30)
    
    try:
        # Créer une fenêtre de test
        root = tk.Tk()
        root.title("Test Interface Améliorée")
        root.geometry("400x300")
        
        # Tester les nouveaux styles
        style = ttk.Style()
        style.theme_use('clam')
        
        # Test des couleurs modernes
        colors = {
            'primary': '#2C3E50',
            'secondary': '#3498DB',
            'success': '#27AE60',
            'background': '#ECF0F1',
            'surface': '#FFFFFF'
        }
        
        # Frame de test
        test_frame = ttk.Frame(root, padding="20")
        test_frame.pack(fill=tk.BOTH, expand=True)
        
        # Titre
        title_label = ttk.Label(test_frame, text="🎓 Test Interface Améliorée", 
                               font=('Segoe UI', 14, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # Test des nouveaux widgets
        ttk.Label(test_frame, text="📐 Orientation:").pack(anchor=tk.W)
        orientation_var = tk.StringVar(value="paysage")
        orientation_frame = ttk.Frame(test_frame)
        orientation_frame.pack(fill=tk.X, pady=(5, 15))
        
        ttk.Radiobutton(orientation_frame, text="📱 Portrait", 
                       variable=orientation_var, value="portrait").pack(side=tk.LEFT, padx=(0, 20))
        ttk.Radiobutton(orientation_frame, text="🖥️ Paysage", 
                       variable=orientation_var, value="paysage").pack(side=tk.LEFT)
        
        # Test des champs de contenu
        ttk.Label(test_frame, text="📝 En-tête personnalisé:").pack(anchor=tk.W)
        header_var = tk.StringVar(value="🎓 TABLEAU D'HONNEUR 🎓")
        ttk.Entry(test_frame, textvariable=header_var).pack(fill=tk.X, pady=(5, 15))
        
        ttk.Label(test_frame, text="🏛️ École:").pack(anchor=tk.W)
        school_var = tk.StringVar(value="École Test")
        ttk.Entry(test_frame, textvariable=school_var).pack(fill=tk.X, pady=(5, 15))
        
        # Boutons de test
        button_frame = ttk.Frame(test_frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))
        
        def test_success():
            messagebox.showinfo("Test", "✅ Interface améliorée fonctionnelle!")
            root.destroy()
        
        def test_cancel():
            root.destroy()
        
        ttk.Button(button_frame, text="✅ Test Réussi", 
                  command=test_success).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="❌ Fermer", 
                  command=test_cancel).pack(side=tk.LEFT)
        
        print("✅ Interface de test créée")
        print("💡 Fermez la fenêtre pour continuer...")
        
        # Centrer la fenêtre
        root.update_idletasks()
        x = (root.winfo_screenwidth() // 2) - (400 // 2)
        y = (root.winfo_screenheight() // 2) - (300 // 2)
        root.geometry(f"400x300+{x}+{y}")
        
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test GUI: {e}")
        return False


def test_csv_compatibility():
    """Teste la compatibilité avec les fichiers CSV existants."""
    print("\n📊 Test de compatibilité CSV")
    print("-" * 30)
    
    csv_files = [
        "data/test_classe_complete.csv",
        "data/test_petite_classe.csv",
        "data/exemple_avec_photos.csv"
    ]
    
    compatible_files = 0
    
    for csv_file in csv_files:
        if os.path.exists(csv_file):
            try:
                import pandas as pd
                df = pd.read_csv(csv_file)
                
                required_columns = ['Nom', 'Note', 'Classement', 'Mention', 'Photo']
                missing_columns = [col for col in required_columns if col not in df.columns]
                
                if not missing_columns:
                    print(f"✅ {csv_file}: Compatible ({len(df)} élèves)")
                    compatible_files += 1
                else:
                    print(f"❌ {csv_file}: Colonnes manquantes: {missing_columns}")
                    
            except Exception as e:
                print(f"❌ {csv_file}: Erreur de lecture: {e}")
        else:
            print(f"⚠️  {csv_file}: Fichier non trouvé")
    
    print(f"\n📊 Résultat: {compatible_files}/{len(csv_files)} fichiers compatibles")
    return compatible_files > 0


def test_new_features():
    """Teste les nouvelles fonctionnalités."""
    print("\n🆕 Test des nouvelles fonctionnalités")
    print("-" * 35)
    
    features_tested = 0
    total_features = 6
    
    # Test 1: Orientation
    try:
        from enhanced_poster_generator import EnhancedCongratulationsPoster
        generator = EnhancedCongratulationsPoster()
        
        # Test portrait
        generator.set_orientation("portrait")
        if generator.poster_height > generator.poster_width:
            print("✅ Orientation portrait: OK")
            features_tested += 1
        else:
            print("❌ Orientation portrait: Échec")
        
        # Test paysage
        generator.set_orientation("paysage")
        if generator.poster_width > generator.poster_height:
            print("✅ Orientation paysage: OK")
            features_tested += 1
        else:
            print("❌ Orientation paysage: Échec")
            
    except Exception as e:
        print(f"❌ Test orientation: {e}")
    
    # Test 2: Dimensions personnalisées
    try:
        generator.set_dimensions(3840, 2160)
        if generator.poster_width == 3840 and generator.poster_height == 2160:
            print("✅ Dimensions personnalisées: OK")
            features_tested += 1
        else:
            print("❌ Dimensions personnalisées: Échec")
    except Exception as e:
        print(f"❌ Test dimensions: {e}")
    
    # Test 3: Contenu personnalisé
    try:
        generator.set_content(
            header_text="Test Header",
            footer_text="Test Footer",
            school_name="École Test",
            academic_year="2024-2025"
        )
        if (generator.header_text == "Test Header" and 
            generator.footer_text == "Test Footer" and
            generator.school_name == "École Test"):
            print("✅ Contenu personnalisé: OK")
            features_tested += 1
        else:
            print("❌ Contenu personnalisé: Échec")
    except Exception as e:
        print(f"❌ Test contenu: {e}")
    
    # Test 4: Style personnalisé
    try:
        generator.set_style(
            background_color="#F0F8FF",
            title_color="#2C3E50",
            border_style="moderne"
        )
        if (generator.background_color == "#F0F8FF" and 
            generator.title_color == "#2C3E50" and
            generator.border_style == "moderne"):
            print("✅ Style personnalisé: OK")
            features_tested += 1
        else:
            print("❌ Style personnalisé: Échec")
    except Exception as e:
        print(f"❌ Test style: {e}")
    
    # Test 5: Conversion couleur hex
    try:
        rgb = generator.hex_to_rgb("#FF0000")
        if rgb == (255, 0, 0):
            print("✅ Conversion couleur hex: OK")
            features_tested += 1
        else:
            print("❌ Conversion couleur hex: Échec")
    except Exception as e:
        print(f"❌ Test conversion couleur: {e}")
    
    print(f"\n🆕 Résultat: {features_tested}/{total_features} nouvelles fonctionnalités OK")
    return features_tested == total_features


def main():
    """Fonction principale de test."""
    print("🧪 TEST COMPLET DE L'INTERFACE AMÉLIORÉE")
    print("=" * 60)
    
    all_tests_passed = True
    
    # Test 1: Composants
    if not test_interface_components():
        all_tests_passed = False
    
    # Test 2: Interface graphique
    if not test_interface_gui():
        all_tests_passed = False
    
    # Test 3: Compatibilité CSV
    if not test_csv_compatibility():
        all_tests_passed = False
    
    # Test 4: Nouvelles fonctionnalités
    if not test_new_features():
        all_tests_passed = False
    
    # Résultat final
    print("\n" + "=" * 60)
    if all_tests_passed:
        print("🎉 TOUS LES TESTS SONT PASSÉS!")
        print("✅ L'interface améliorée est prête à l'emploi")
        print("\n💡 Pour lancer l'interface:")
        print("   python lancer_interface.py")
        print("   ou double-cliquez sur Lancer_Interface.bat")
    else:
        print("⚠️  CERTAINS TESTS ONT ÉCHOUÉ")
        print("🔧 Vérifiez les erreurs ci-dessus")
        print("\n💡 Pour réinstaller:")
        print("   python setup.py")
    
    input("\nAppuyez sur Entrée pour quitter...")


if __name__ == "__main__":
    main()
