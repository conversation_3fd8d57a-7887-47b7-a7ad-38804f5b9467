#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de la hauteur dynamique selon le nombre d'élèves
"""

import os
import pandas as pd
from congratulations_poster import Congratulations<PERSON>oster

def create_test_data_with_students(num_students):
    """Crée des données de test avec un nombre spécifique d'élèves."""

    # Noms d'élèves
    first_names = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
                   '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
                   '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
                   '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>']

    last_names = ['<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>',
                  '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>',
                  '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>uo<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Indigo']

    # Gén<PERSON>rer les données
    names = []
    notes = []
    mentions = []

    for i in range(num_students):
        # Nom
        first = first_names[i % len(first_names)]
        last = last_names[i % len(last_names)]
        names.append(f"{first} {last}")

        # Note décroissante
        note = max(10.0, 20.0 - (i * 0.5))
        notes.append(round(note, 1))

        # Mention selon la note
        if note >= 16:
            mentions.append('Très Bien')
        elif note >= 14:
            mentions.append('Bien')
        elif note >= 12:
            mentions.append('Assez Bien')
        else:
            mentions.append('Passable')

    sample_data = {
        'Nom': names,
        'Note': notes,
        'Classement': list(range(1, num_students + 1)),
        'Mention': mentions,
        'Photo': [f'photos/eleve_{i+1}.jpg' for i in range(num_students)]
    }

    # Créer les dossiers nécessaires
    directories = ['data', 'photos', 'templates', 'output']
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)

    # Sauvegarder le CSV
    df = pd.DataFrame(sample_data)
    filename = f'data/test_{num_students}_eleves.csv'
    df.to_csv(filename, index=False, encoding='utf-8')

    return filename

def test_dynamic_height():
    """Test la hauteur dynamique avec différents nombres d'élèves."""
    print("🎯 Test de la Hauteur Dynamique")
    print("=" * 50)

    # Différents nombres d'élèves à tester
    test_cases = [3, 5, 8, 12, 16, 20, 24, 30]

    # Initialiser le générateur
    template_path = 'templates/fond_defaut.png'
    generator = CongratulationsPoster(template_path)

    print(f"\n📊 Tests avec différents nombres d'élèves:")
    print(f"{'Élèves':<8} {'Hauteur':<8} {'Lignes':<8} {'Fichier':<25}")
    print("-" * 60)

    results = []

    for num_students in test_cases:
        # Créer les données de test
        csv_path = create_test_data_with_students(num_students)

        # Calculer la hauteur dynamique
        calculated_height = generator.calculate_dynamic_height(num_students)

        # Calculer le nombre de lignes d'élèves après le podium
        if num_students > 3:
            remaining = num_students - 3
            lines = math.ceil(remaining / 4)
        else:
            lines = 0

        # Générer l'affiche
        class_name = f"Test {num_students} élèves"
        output_path = generator.generate_poster(csv_path, class_name)

        # Nom du fichier de sortie
        output_filename = os.path.basename(output_path) if output_path else "Erreur"

        print(f"{num_students:<8} {calculated_height:<8} {lines:<8} {output_filename:<25}")

        results.append({
            'students': num_students,
            'height': calculated_height,
            'lines': lines,
            'file': output_filename
        })

    print("\n" + "=" * 60)
    print("📈 Analyse des résultats:")

    # Analyser la progression
    for i, result in enumerate(results):
        if i > 0:
            height_diff = result['height'] - results[i-1]['height']
            student_diff = result['students'] - results[i-1]['students']
            print(f"   {results[i-1]['students']} → {result['students']} élèves: "
                  f"+{height_diff}px (+{student_diff} élèves)")

    print(f"\n✅ Hauteurs calculées:")
    print(f"   - Minimum: {min(r['height'] for r in results)}px")
    print(f"   - Maximum: {max(r['height'] for r in results)}px")
    print(f"   - Moyenne: {sum(r['height'] for r in results) // len(results)}px")

    return results

def test_edge_cases():
    """Test les cas limites."""
    print(f"\n🔍 Test des cas limites:")
    print("-" * 30)

    generator = CongratulationsPoster('templates/fond_defaut.png')

    edge_cases = [1, 2, 3, 4, 50, 100]

    for num in edge_cases:
        height = generator.calculate_dynamic_height(num)
        print(f"   {num:3d} élève(s): {height:4d}px")

def main():
    """Fonction principale de test."""
    print("📏 Test de la Hauteur Dynamique des Affiches")
    print("=" * 50)

    # Importer math pour les calculs
    import math
    globals()['math'] = math

    # Test principal
    results = test_dynamic_height()

    # Test des cas limites
    test_edge_cases()

    print(f"\n🎉 Tests terminés!")
    print(f"💡 La hauteur de l'affiche s'adapte automatiquement:")
    print(f"   ✅ Hauteur minimale: 600px")
    print(f"   ✅ Hauteur maximale: 4000px")
    print(f"   ✅ Calcul basé sur le nombre d'élèves")
    print(f"   ✅ Prise en compte du podium (3 premiers)")
    print(f"   ✅ Organisation en lignes de 4 élèves")

    # Recommandations
    print(f"\n💡 Recommandations:")
    print(f"   - Jusqu'à 12 élèves: Format compact")
    print(f"   - 13-20 élèves: Format standard")
    print(f"   - Plus de 20 élèves: Format étendu")

if __name__ == "__main__":
    main()
