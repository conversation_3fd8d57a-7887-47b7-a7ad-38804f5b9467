#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Interface graphique professionnelle moderne pour le générateur d'affiches
Avec personnalisation avancée et aperçu en temps réel
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, colorchooser
import threading
import os
from PIL import Image, ImageTk, ImageDraw, ImageFont
import pandas as pd
from congratulations_poster import CongratulationsPoster
import json

class ModernPosterApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎓 Générateur d'Affiches Professionnel")
        self.root.geometry("1600x1000")
        self.root.state('zoomed')

        # Variables de configuration
        self.setup_variables()

        # Style moderne
        self.setup_modern_theme()

        # Menu principal
        self.setup_menu_bar()

        # Interface utilisateur
        self.setup_professional_ui()

        # Générateur et aperçu
        self.generator = None
        self.current_poster = None
        self.preview_image = None
        self.zoom_factor = 1.0

        # Chargement initial
        self.load_default_settings()

    def setup_menu_bar(self):
        """Configure la barre de menu principale."""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # Menu Fichier
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="📁 Fichier", menu=file_menu)
        file_menu.add_command(label="📊 Ouvrir CSV...", command=self.browse_csv, accelerator="Ctrl+O")
        file_menu.add_separator()
        file_menu.add_command(label="💾 Sauvegarder Config...", command=self.save_configuration, accelerator="Ctrl+S")
        file_menu.add_command(label="📂 Charger Config...", command=self.load_configuration, accelerator="Ctrl+L")
        file_menu.add_separator()
        file_menu.add_command(label="🖼️ Exporter PNG...", command=self.export_poster, accelerator="Ctrl+E")
        file_menu.add_separator()
        file_menu.add_command(label="❌ Quitter", command=self.root.quit, accelerator="Ctrl+Q")

        # Menu Édition
        edit_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="✏️ Édition", menu=edit_menu)
        edit_menu.add_command(label="🔄 Actualiser Aperçu", command=self.refresh_preview, accelerator="F5")
        edit_menu.add_command(label="🎯 Ajuster Zoom", command=self.zoom_fit, accelerator="Ctrl+0")
        edit_menu.add_separator()
        edit_menu.add_command(label="🏫 Preset École", command=lambda: self.apply_preset("school"))
        edit_menu.add_command(label="🎓 Preset Diplôme", command=lambda: self.apply_preset("graduation"))
        edit_menu.add_command(label="🏆 Preset Sport", command=lambda: self.apply_preset("sport"))
        edit_menu.add_separator()
        edit_menu.add_command(label="↩️ Paramètres par défaut", command=self.reset_to_defaults)

        # Menu Affichage
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="👁️ Affichage", menu=view_menu)
        view_menu.add_command(label="🔍+ Zoom Avant", command=self.zoom_in, accelerator="Ctrl++")
        view_menu.add_command(label="🔍- Zoom Arrière", command=self.zoom_out, accelerator="Ctrl+-")
        view_menu.add_command(label="🎯 Ajuster à la fenêtre", command=self.zoom_fit, accelerator="Ctrl+0")

        # Menu Outils
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="🔧 Outils", menu=tools_menu)
        tools_menu.add_command(label="📊 Analyser CSV", command=self.analyze_csv)
        tools_menu.add_command(label="🎨 Générer Aperçu", command=self.generate_preview, accelerator="Ctrl+G")
        tools_menu.add_separator()
        tools_menu.add_command(label="📐 Calculateur de hauteur", command=self.show_height_calculator)
        tools_menu.add_command(label="🎨 Générateur de couleurs", command=self.show_color_generator)

        # Menu Aide
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="❓ Aide", menu=help_menu)
        help_menu.add_command(label="📖 Guide d'utilisation", command=self.show_user_guide)
        help_menu.add_command(label="⌨️ Raccourcis clavier", command=self.show_shortcuts)
        help_menu.add_separator()
        help_menu.add_command(label="ℹ️ À propos", command=self.show_about)

        # Raccourcis clavier
        self.setup_keyboard_shortcuts()

    def setup_keyboard_shortcuts(self):
        """Configure les raccourcis clavier."""
        self.root.bind('<Control-o>', lambda e: self.browse_csv())
        self.root.bind('<Control-s>', lambda e: self.save_configuration())
        self.root.bind('<Control-l>', lambda e: self.load_configuration())
        self.root.bind('<Control-e>', lambda e: self.export_poster())
        self.root.bind('<Control-q>', lambda e: self.root.quit())
        self.root.bind('<F5>', lambda e: self.refresh_preview())
        self.root.bind('<Control-g>', lambda e: self.generate_preview())
        self.root.bind('<Control-0>', lambda e: self.zoom_fit())
        self.root.bind('<Control-plus>', lambda e: self.zoom_in())
        self.root.bind('<Control-minus>', lambda e: self.zoom_out())

    def show_height_calculator(self):
        """Affiche le calculateur de hauteur."""
        calc_window = tk.Toplevel(self.root)
        calc_window.title("📐 Calculateur de Hauteur")
        calc_window.geometry("400x300")
        calc_window.resizable(False, False)

        # Centrer la fenêtre
        calc_window.transient(self.root)
        calc_window.grab_set()

        frame = ttk.Frame(calc_window, padding="20")
        frame.pack(fill=tk.BOTH, expand=True)

        ttk.Label(frame, text="📐 Calculateur de Hauteur d'Affiche",
                 font=('Segoe UI', 14, 'bold')).pack(pady=(0, 20))

        # Nombre d'élèves
        students_frame = ttk.Frame(frame)
        students_frame.pack(fill=tk.X, pady=5)
        ttk.Label(students_frame, text="Nombre d'élèves:", width=15).pack(side=tk.LEFT)
        students_var = tk.IntVar(value=12)
        ttk.Spinbox(students_frame, from_=1, to=100, textvariable=students_var, width=10).pack(side=tk.LEFT)

        # Résultat
        result_frame = ttk.LabelFrame(frame, text="Résultat", padding="10")
        result_frame.pack(fill=tk.X, pady=20)

        result_text = tk.Text(result_frame, height=8, width=40, wrap=tk.WORD)
        result_text.pack(fill=tk.BOTH, expand=True)

        def calculate():
            num_students = students_var.get()
            if self.generator:
                height = self.generator.calculate_dynamic_height(num_students)
            else:
                # Calcul approximatif
                base_height = 400
                if num_students > 3:
                    remaining = num_students - 3
                    rows = (remaining + 9) // 10  # 10 par ligne
                    height = base_height + (rows * 180)
                else:
                    height = base_height

            result = f"📊 Calcul pour {num_students} élève(s)\n"
            result += f"=" * 30 + "\n\n"
            result += f"📏 Hauteur recommandée: {height}px\n\n"

            if num_students <= 3:
                result += f"📐 Disposition: Podium seulement\n"
            else:
                remaining = num_students - 3
                rows = (remaining + 9) // 10
                result += f"📐 Disposition:\n"
                result += f"   • Podium: 3 élèves\n"
                result += f"   • Autres: {remaining} élèves\n"
                result += f"   • Lignes: {rows} (10 par ligne)\n"

            result += f"\n💡 Cette hauteur optimise l'espace\n"
            result += f"   et évite les espaces vides."

            result_text.delete(1.0, tk.END)
            result_text.insert(1.0, result)

        # Boutons
        button_frame = ttk.Frame(frame)
        button_frame.pack(fill=tk.X, pady=10)

        ttk.Button(button_frame, text="🔢 Calculer", command=calculate).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="📋 Appliquer",
                  command=lambda: self.poster_height.set(students_var.get())).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="❌ Fermer", command=calc_window.destroy).pack(side=tk.RIGHT)

        # Calcul initial
        calculate()

    def show_color_generator(self):
        """Affiche le générateur de couleurs."""
        color_window = tk.Toplevel(self.root)
        color_window.title("🎨 Générateur de Couleurs")
        color_window.geometry("500x400")
        color_window.resizable(False, False)

        # Centrer la fenêtre
        color_window.transient(self.root)
        color_window.grab_set()

        frame = ttk.Frame(color_window, padding="20")
        frame.pack(fill=tk.BOTH, expand=True)

        ttk.Label(frame, text="🎨 Générateur de Palettes de Couleurs",
                 font=('Segoe UI', 14, 'bold')).pack(pady=(0, 20))

        # Palettes prédéfinies
        palettes = {
            "🏫 École Classique": ["#E8F4FD", "#1E3A8A", "#374151", "#3B82F6"],
            "🎓 Diplôme Doré": ["#FEF3C7", "#92400E", "#451A03", "#F59E0B"],
            "🏆 Sport Vert": ["#DCFCE7", "#166534", "#14532D", "#22C55E"],
            "🌸 Printemps": ["#FDF2F8", "#BE185D", "#831843", "#EC4899"],
            "🌊 Océan": ["#E0F2FE", "#0C4A6E", "#164E63", "#0891B2"],
            "🍂 Automne": ["#FEF3E2", "#9A3412", "#7C2D12", "#EA580C"]
        }

        for palette_name, colors in palettes.items():
            palette_frame = ttk.LabelFrame(frame, text=palette_name, padding="10")
            palette_frame.pack(fill=tk.X, pady=5)

            colors_frame = ttk.Frame(palette_frame)
            colors_frame.pack(fill=tk.X)

            # Afficher les couleurs
            for i, color in enumerate(colors):
                color_frame = ttk.Frame(colors_frame)
                color_frame.pack(side=tk.LEFT, padx=5)

                # Carré de couleur
                color_canvas = tk.Canvas(color_frame, width=30, height=30, bg=color, relief='solid', bd=1)
                color_canvas.pack()

                # Code couleur
                ttk.Label(color_frame, text=color, font=('Courier', 8)).pack()

            # Bouton appliquer
            def apply_palette(colors=colors):
                self.background_color.set(colors[0])
                self.title_color.set(colors[1])
                self.text_color.set(colors[2])
                self.accent_color.set(colors[3])
                self.update_color_buttons()
                self.refresh_preview()
                color_window.destroy()

            ttk.Button(palette_frame, text="✅ Appliquer", command=apply_palette).pack(side=tk.RIGHT)

        # Bouton fermer
        ttk.Button(frame, text="❌ Fermer", command=color_window.destroy).pack(pady=20)

    def show_user_guide(self):
        """Affiche le guide d'utilisation."""
        guide_window = tk.Toplevel(self.root)
        guide_window.title("📖 Guide d'Utilisation")
        guide_window.geometry("600x500")

        # Centrer la fenêtre
        guide_window.transient(self.root)

        frame = ttk.Frame(guide_window, padding="20")
        frame.pack(fill=tk.BOTH, expand=True)

        # Texte du guide
        text_widget = tk.Text(frame, wrap=tk.WORD, padx=10, pady=10)
        scrollbar = ttk.Scrollbar(frame, orient="vertical", command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        guide_text = """📖 Guide d'Utilisation - Générateur d'Affiches Professionnel

🚀 DÉMARRAGE RAPIDE
1. Sélectionnez votre fichier CSV dans l'onglet "📁 Fichiers"
2. Personnalisez les couleurs et la mise en page
3. Cliquez sur "🎨 Générer Aperçu"
4. Utilisez le zoom pour examiner les détails
5. Exportez votre affiche avec "💾 Exporter PNG"

📁 ONGLET FICHIERS
• 📊 Données CSV : Fichier avec colonnes Nom, Note, Classement, Mention
• 🎨 Template : Image de fond personnalisée (optionnel)
• 📋 En-tête : Logo ou titre de l'école (optionnel)
• 📄 Pied de page : Message de félicitations (optionnel)
• 💾 Sortie : Dossier où sauvegarder les affiches

📝 ONGLET CONTENU
• 🏫 Nom de classe : Titre principal de l'affiche
• 🎓 École : Nom de l'établissement
• 📅 Année scolaire : Période concernée

📐 ONGLET LAYOUT
• 📏 Dimensions : Largeur et hauteur en pixels
• 🔄 Orientation : Portrait ou Paysage
• 📐 Espacement : Nombre d'élèves par ligne, taille des photos

🎨 ONGLET STYLE
• 🎨 Couleurs : Fond, titre, texte, accent avec sélecteurs visuels
• 🔤 Polices : Tailles pour titre et noms d'élèves

⚙️ ONGLET AVANCÉ
• ⚙️ Options : Numéros sur chapeaux, podium spécial, décorations
• 📐 Espacement Avancé : Contrôles fins pour utilisateurs experts

👁️ APERÇU EN TEMPS RÉEL
• 🔍+ 🔍- : Zoom avant/arrière
• 🎯 : Ajuster à la fenêtre
• Molette + Ctrl : Zoom avec la souris
• Molette seule : Défilement vertical

🎨 PERSONNALISATION RAPIDE
• Sélecteurs de couleurs avec aperçu immédiat
• Presets prédéfinis (École, Diplôme, Sport)
• Contrôles de dimensions en temps réel

⌨️ RACCOURCIS CLAVIER
• Ctrl+O : Ouvrir CSV
• Ctrl+S : Sauvegarder configuration
• Ctrl+E : Exporter PNG
• F5 : Actualiser aperçu
• Ctrl+G : Générer aperçu
• Ctrl+0 : Ajuster zoom

💡 CONSEILS
• Utilisez des images PNG avec transparence pour l'en-tête/pied de page
• La hauteur automatique optimise l'espace selon le nombre d'élèves
• Les presets appliquent des couleurs harmonieuses instantanément
• Sauvegardez vos configurations pour les réutiliser

🎯 FORMAT CSV REQUIS
Nom,Note,Classement,Mention,Photo
Alice Martin,18.5,1,Très Bien,photos/alice.jpg
Bob Dupont,17.2,2,Très Bien,photos/bob.jpg
...

📞 SUPPORT
En cas de problème, vérifiez que :
• Le fichier CSV a les bonnes colonnes
• Les images existent (en-tête, pied de page, photos)
• Les dimensions sont raisonnables (800-4000px)
"""

        text_widget.insert(tk.END, guide_text)
        text_widget.config(state=tk.DISABLED)

        text_widget.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Bouton fermer
        ttk.Button(frame, text="❌ Fermer", command=guide_window.destroy).pack(pady=10)

    def show_shortcuts(self):
        """Affiche les raccourcis clavier."""
        shortcuts_window = tk.Toplevel(self.root)
        shortcuts_window.title("⌨️ Raccourcis Clavier")
        shortcuts_window.geometry("400x350")
        shortcuts_window.resizable(False, False)

        # Centrer la fenêtre
        shortcuts_window.transient(self.root)
        shortcuts_window.grab_set()

        frame = ttk.Frame(shortcuts_window, padding="20")
        frame.pack(fill=tk.BOTH, expand=True)

        ttk.Label(frame, text="⌨️ Raccourcis Clavier",
                 font=('Segoe UI', 14, 'bold')).pack(pady=(0, 20))

        shortcuts = [
            ("Ctrl + O", "Ouvrir fichier CSV"),
            ("Ctrl + S", "Sauvegarder configuration"),
            ("Ctrl + L", "Charger configuration"),
            ("Ctrl + E", "Exporter PNG"),
            ("Ctrl + Q", "Quitter l'application"),
            ("F5", "Actualiser l'aperçu"),
            ("Ctrl + G", "Générer aperçu"),
            ("Ctrl + 0", "Ajuster le zoom"),
            ("Ctrl + +", "Zoom avant"),
            ("Ctrl + -", "Zoom arrière"),
            ("Ctrl + Molette", "Zoom avec la souris"),
            ("Molette", "Défilement vertical")
        ]

        for shortcut, description in shortcuts:
            shortcut_frame = ttk.Frame(frame)
            shortcut_frame.pack(fill=tk.X, pady=2)

            ttk.Label(shortcut_frame, text=shortcut, font=('Courier', 10, 'bold'),
                     width=15).pack(side=tk.LEFT)
            ttk.Label(shortcut_frame, text=description).pack(side=tk.LEFT, padx=(10, 0))

        # Bouton fermer
        ttk.Button(frame, text="❌ Fermer", command=shortcuts_window.destroy).pack(pady=20)

    def show_about(self):
        """Affiche les informations sur l'application."""
        about_window = tk.Toplevel(self.root)
        about_window.title("ℹ️ À propos")
        about_window.geometry("450x300")
        about_window.resizable(False, False)

        # Centrer la fenêtre
        about_window.transient(self.root)
        about_window.grab_set()

        frame = ttk.Frame(about_window, padding="30")
        frame.pack(fill=tk.BOTH, expand=True)

        # Titre
        ttk.Label(frame, text="🎓 Générateur d'Affiches Professionnel",
                 font=('Segoe UI', 16, 'bold')).pack(pady=(0, 10))

        # Version
        ttk.Label(frame, text="Version 2.0 - Interface Professionnelle",
                 font=('Segoe UI', 12)).pack(pady=(0, 20))

        # Description
        description = """Une application moderne et professionnelle pour créer
des affiches de tableaux d'honneur personnalisées.

✨ Fonctionnalités principales :
• Interface moderne avec aperçu en temps réel
• Personnalisation complète des couleurs et mise en page
• Support des images d'en-tête et de pied de page
• Presets prédéfinis pour différents styles
• Export haute résolution PNG/JPEG
• Sauvegarde/chargement de configurations

🎯 Conçu pour les établissements scolaires
qui exigent qualité et simplicité d'utilisation."""

        ttk.Label(frame, text=description, justify=tk.CENTER,
                 wraplength=350).pack(pady=(0, 20))

        # Copyright
        ttk.Label(frame, text="© 2024 - Interface Professionnelle",
                 font=('Segoe UI', 10, 'italic')).pack(pady=(0, 20))

        # Bouton fermer
        ttk.Button(frame, text="❌ Fermer", command=about_window.destroy).pack()

    def setup_variables(self):
        """Initialise toutes les variables de configuration."""
        # Fichiers
        self.csv_file = tk.StringVar()
        self.template_file = tk.StringVar(value="templates/fond_defaut.png")
        self.header_image = tk.StringVar()
        self.footer_image = tk.StringVar()
        self.output_dir = tk.StringVar(value="output")

        # Contenu
        self.class_name = tk.StringVar(value="Ma Classe")
        self.school_name = tk.StringVar(value="École Exemple")
        self.academic_year = tk.StringVar(value="2024-2025")

        # Mise en page
        self.orientation = tk.StringVar(value="paysage")
        self.poster_width = tk.IntVar(value=1920)
        self.poster_height = tk.IntVar(value=1080)
        self.photo_size = tk.IntVar(value=120)
        self.students_per_row = tk.IntVar(value=8)

        # Style et couleurs
        self.background_color = tk.StringVar(value="#FFD700")
        self.title_color = tk.StringVar(value="#2C3E50")
        self.text_color = tk.StringVar(value="#2C3E50")
        self.accent_color = tk.StringVar(value="#F1C40F")

        # Polices
        self.title_font_size = tk.IntVar(value=52)
        self.name_font_size = tk.IntVar(value=22)
        self.info_font_size = tk.IntVar(value=16)

        # Options avancées
        self.show_numbers = tk.BooleanVar(value=True)
        self.show_podium = tk.BooleanVar(value=True)
        self.show_decorations = tk.BooleanVar(value=True)
        self.auto_height = tk.BooleanVar(value=True)

        # Espacement
        self.podium_spacing = tk.IntVar(value=350)
        self.regular_spacing_x = tk.IntVar(value=180)
        self.regular_spacing_y = tk.IntVar(value=200)

    def setup_modern_theme(self):
        """Configure le thème moderne professionnel."""
        style = ttk.Style()
        style.theme_use('clam')

        # Couleurs du thème
        self.theme_colors = {
            'primary': '#2C3E50',
            'secondary': '#3498DB',
            'accent': '#E74C3C',
            'success': '#27AE60',
            'warning': '#F39C12',
            'background': '#F8F9FA',
            'surface': '#FFFFFF',
            'text': '#2C3E50',
            'text_light': '#7F8C8D',
            'border': '#E9ECEF'
        }

        # Configuration des styles
        style.configure('Title.TLabel',
                       font=('Segoe UI', 18, 'bold'),
                       foreground=self.theme_colors['primary'],
                       background=self.theme_colors['background'])

        style.configure('Heading.TLabel',
                       font=('Segoe UI', 12, 'bold'),
                       foreground=self.theme_colors['text'])

        style.configure('Modern.TFrame',
                       background=self.theme_colors['surface'],
                       relief='flat')

        style.configure('Card.TFrame',
                       background=self.theme_colors['surface'],
                       relief='solid',
                       borderwidth=1)

        style.configure('Primary.TButton',
                       font=('Segoe UI', 10, 'bold'))

        style.map('Primary.TButton',
                 background=[('active', self.theme_colors['secondary']),
                           ('!active', self.theme_colors['primary'])],
                 foreground=[('active', 'white'), ('!active', 'white')])

    def setup_professional_ui(self):
        """Configure l'interface utilisateur professionnelle."""
        # Conteneur principal
        main_container = ttk.Frame(self.root, style='Modern.TFrame')
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Configuration du grid principal
        main_container.columnconfigure(0, weight=1, minsize=400)  # Panel de contrôle
        main_container.columnconfigure(1, weight=2, minsize=800)  # Panel d'aperçu
        main_container.rowconfigure(0, weight=1)

        # Panel de contrôle gauche
        self.setup_control_panel(main_container)

        # Panel d'aperçu droite avec personnalisation intégrée
        self.setup_integrated_preview_panel(main_container)

        # Barre d'état
        self.setup_status_bar()

    def setup_control_panel(self, parent):
        """Configure le panel de contrôle avec onglets."""
        control_frame = ttk.LabelFrame(parent, text="⚙️ Configuration", padding="10")
        control_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))

        # Notebook pour les onglets
        notebook = ttk.Notebook(control_frame)
        notebook.pack(fill=tk.BOTH, expand=True)

        # Onglets
        self.setup_files_tab(notebook)
        self.setup_content_tab(notebook)
        self.setup_layout_tab(notebook)
        self.setup_style_tab(notebook)
        self.setup_advanced_tab(notebook)

    def setup_content_tab(self, notebook):
        """Onglet de contenu."""
        content_frame = ttk.Frame(notebook)
        notebook.add(content_frame, text="📝 Contenu")

        # Scroll frame
        canvas = tk.Canvas(content_frame)
        scrollbar = ttk.Scrollbar(content_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Contenu
        self.create_text_section(scrollable_frame, "🏫 Nom de classe", self.class_name)
        self.create_text_section(scrollable_frame, "🎓 École", self.school_name)
        self.create_text_section(scrollable_frame, "📅 Année scolaire", self.academic_year)

    def setup_layout_tab(self, notebook):
        """Onglet de mise en page."""
        layout_frame = ttk.Frame(notebook)
        notebook.add(layout_frame, text="📐 Layout")

        # Scroll frame
        canvas = tk.Canvas(layout_frame)
        scrollbar = ttk.Scrollbar(layout_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Dimensions
        dim_frame = ttk.LabelFrame(scrollable_frame, text="📏 Dimensions", padding="10")
        dim_frame.pack(fill=tk.X, pady=5)

        # Largeur
        width_frame = ttk.Frame(dim_frame)
        width_frame.pack(fill=tk.X, pady=2)
        ttk.Label(width_frame, text="Largeur:", width=12).pack(side=tk.LEFT)
        ttk.Spinbox(width_frame, from_=800, to=4000, textvariable=self.poster_width,
                   width=8, command=self.refresh_preview).pack(side=tk.LEFT)

        # Hauteur
        height_frame = ttk.Frame(dim_frame)
        height_frame.pack(fill=tk.X, pady=2)
        ttk.Label(height_frame, text="Hauteur:", width=12).pack(side=tk.LEFT)
        ttk.Spinbox(height_frame, from_=600, to=3000, textvariable=self.poster_height,
                   width=8, command=self.refresh_preview).pack(side=tk.LEFT)

        # Orientation
        orient_frame = ttk.LabelFrame(scrollable_frame, text="🔄 Orientation", padding="10")
        orient_frame.pack(fill=tk.X, pady=5)

        ttk.Radiobutton(orient_frame, text="📱 Portrait", variable=self.orientation,
                       value="portrait", command=self.on_orientation_change).pack(anchor=tk.W)
        ttk.Radiobutton(orient_frame, text="🖥️ Paysage", variable=self.orientation,
                       value="paysage", command=self.on_orientation_change).pack(anchor=tk.W)

        # Espacement
        spacing_frame = ttk.LabelFrame(scrollable_frame, text="📐 Espacement", padding="10")
        spacing_frame.pack(fill=tk.X, pady=5)

        # Élèves par ligne
        students_frame = ttk.Frame(spacing_frame)
        students_frame.pack(fill=tk.X, pady=2)
        ttk.Label(students_frame, text="Élèves/ligne:", width=12).pack(side=tk.LEFT)
        ttk.Spinbox(students_frame, from_=3, to=15, textvariable=self.students_per_row,
                   width=4, command=self.refresh_preview).pack(side=tk.LEFT)

        # Taille des photos
        photo_frame = ttk.Frame(spacing_frame)
        photo_frame.pack(fill=tk.X, pady=2)
        ttk.Label(photo_frame, text="Taille photo:", width=12).pack(side=tk.LEFT)
        ttk.Spinbox(photo_frame, from_=80, to=200, textvariable=self.photo_size,
                   width=4, command=self.refresh_preview).pack(side=tk.LEFT)

    def setup_style_tab(self, notebook):
        """Onglet de style."""
        style_frame = ttk.Frame(notebook)
        notebook.add(style_frame, text="🎨 Style")

        # Scroll frame
        canvas = tk.Canvas(style_frame)
        scrollbar = ttk.Scrollbar(style_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Couleurs
        color_frame = ttk.LabelFrame(scrollable_frame, text="🎨 Couleurs", padding="10")
        color_frame.pack(fill=tk.X, pady=5)

        colors = [
            ("Fond", self.background_color, "#FFD700"),
            ("Titre", self.title_color, "#2C3E50"),
            ("Texte", self.text_color, "#2C3E50"),
            ("Accent", self.accent_color, "#F1C40F")
        ]

        for label, variable, default in colors:
            self.create_color_picker(color_frame, label, variable, default)

        # Polices
        font_frame = ttk.LabelFrame(scrollable_frame, text="🔤 Polices", padding="10")
        font_frame.pack(fill=tk.X, pady=5)

        # Taille titre
        title_font_frame = ttk.Frame(font_frame)
        title_font_frame.pack(fill=tk.X, pady=2)
        ttk.Label(title_font_frame, text="Titre:", width=12).pack(side=tk.LEFT)
        ttk.Spinbox(title_font_frame, from_=24, to=72, textvariable=self.title_font_size,
                   width=4, command=self.refresh_preview).pack(side=tk.LEFT)

        # Taille nom
        name_font_frame = ttk.Frame(font_frame)
        name_font_frame.pack(fill=tk.X, pady=2)
        ttk.Label(name_font_frame, text="Nom:", width=12).pack(side=tk.LEFT)
        ttk.Spinbox(name_font_frame, from_=12, to=36, textvariable=self.name_font_size,
                   width=4, command=self.refresh_preview).pack(side=tk.LEFT)

    def setup_advanced_tab(self, notebook):
        """Onglet des options avancées."""
        advanced_frame = ttk.Frame(notebook)
        notebook.add(advanced_frame, text="⚙️ Avancé")

        # Scroll frame
        canvas = tk.Canvas(advanced_frame)
        scrollbar = ttk.Scrollbar(advanced_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Options
        options_frame = ttk.LabelFrame(scrollable_frame, text="⚙️ Options", padding="10")
        options_frame.pack(fill=tk.X, pady=5)

        options = [
            ("Numéros sur chapeaux", self.show_numbers),
            ("Podium spécial", self.show_podium),
            ("Décorations", self.show_decorations),
            ("Hauteur automatique", self.auto_height)
        ]

        for text, variable in options:
            cb = ttk.Checkbutton(options_frame, text=text, variable=variable,
                               command=self.refresh_preview)
            cb.pack(anchor=tk.W, pady=1)

        # Espacement avancé
        spacing_frame = ttk.LabelFrame(scrollable_frame, text="📐 Espacement Avancé", padding="10")
        spacing_frame.pack(fill=tk.X, pady=5)

        # Podium
        podium_frame = ttk.Frame(spacing_frame)
        podium_frame.pack(fill=tk.X, pady=2)
        ttk.Label(podium_frame, text="Podium:", width=12).pack(side=tk.LEFT)
        ttk.Spinbox(podium_frame, from_=200, to=500, textvariable=self.podium_spacing,
                   width=6, command=self.refresh_preview).pack(side=tk.LEFT)

        # Horizontal
        h_spacing_frame = ttk.Frame(spacing_frame)
        h_spacing_frame.pack(fill=tk.X, pady=2)
        ttk.Label(h_spacing_frame, text="Horizontal:", width=12).pack(side=tk.LEFT)
        ttk.Spinbox(h_spacing_frame, from_=100, to=300, textvariable=self.regular_spacing_x,
                   width=6, command=self.refresh_preview).pack(side=tk.LEFT)

        # Vertical
        v_spacing_frame = ttk.Frame(spacing_frame)
        v_spacing_frame.pack(fill=tk.X, pady=2)
        ttk.Label(v_spacing_frame, text="Vertical:", width=12).pack(side=tk.LEFT)
        ttk.Spinbox(v_spacing_frame, from_=150, to=300, textvariable=self.regular_spacing_y,
                   width=6, command=self.refresh_preview).pack(side=tk.LEFT)

    def create_text_section(self, parent, label, variable):
        """Crée une section de texte."""
        frame = ttk.LabelFrame(parent, text=label, padding="5")
        frame.pack(fill=tk.X, pady=5)

        entry = ttk.Entry(frame, textvariable=variable, font=('Segoe UI', 10))
        entry.pack(fill=tk.X)
        entry.bind('<KeyRelease>', lambda e: self.refresh_preview())

    def setup_files_tab(self, notebook):
        """Onglet de gestion des fichiers."""
        files_frame = ttk.Frame(notebook)
        notebook.add(files_frame, text="📁 Fichiers")

        # Scroll frame
        canvas = tk.Canvas(files_frame)
        scrollbar = ttk.Scrollbar(files_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Contenu des fichiers
        self.create_file_section(scrollable_frame, "📊 Données CSV", self.csv_file, self.browse_csv)
        self.create_file_section(scrollable_frame, "🎨 Template", self.template_file, self.browse_template)
        self.create_file_section(scrollable_frame, "📋 En-tête", self.header_image, self.browse_header)
        self.create_file_section(scrollable_frame, "📄 Pied de page", self.footer_image, self.browse_footer)
        self.create_file_section(scrollable_frame, "💾 Sortie", self.output_dir, self.browse_output, is_dir=True)

        # Boutons d'action
        action_frame = ttk.Frame(scrollable_frame)
        action_frame.pack(fill=tk.X, pady=10)

        ttk.Button(action_frame, text="📊 Analyser CSV",
                  command=self.analyze_csv, style='Primary.TButton').pack(fill=tk.X, pady=2)
        ttk.Button(action_frame, text="🔄 Actualiser",
                  command=self.refresh_preview, style='Primary.TButton').pack(fill=tk.X, pady=2)

    def create_file_section(self, parent, label, variable, command, is_dir=False):
        """Crée une section de sélection de fichier."""
        frame = ttk.LabelFrame(parent, text=label, padding="5")
        frame.pack(fill=tk.X, pady=5)

        entry_frame = ttk.Frame(frame)
        entry_frame.pack(fill=tk.X)

        entry = ttk.Entry(entry_frame, textvariable=variable, font=('Segoe UI', 9))
        entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        ttk.Button(entry_frame, text="📂", command=command, width=3).pack(side=tk.RIGHT, padx=(5, 0))

    def setup_integrated_preview_panel(self, parent):
        """Panel d'aperçu avec personnalisation intégrée."""
        preview_container = ttk.Frame(parent)
        preview_container.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configuration du grid
        preview_container.columnconfigure(0, weight=1)
        preview_container.rowconfigure(1, weight=1)

        # Barre d'outils d'aperçu
        self.setup_preview_toolbar(preview_container)

        # Zone d'aperçu principale
        self.setup_main_preview_area(preview_container)

        # Panel de personnalisation rapide (côté droit)
        self.setup_quick_customization_panel(preview_container)

    def setup_preview_toolbar(self, parent):
        """Barre d'outils pour l'aperçu."""
        toolbar = ttk.Frame(parent, style='Card.TFrame')
        toolbar.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        # Titre
        ttk.Label(toolbar, text="👁️ Aperçu en Temps Réel",
                 style='Heading.TLabel').pack(side=tk.LEFT, padx=10)

        # Contrôles de zoom
        zoom_frame = ttk.Frame(toolbar)
        zoom_frame.pack(side=tk.RIGHT, padx=10)

        ttk.Button(zoom_frame, text="🔍-", command=self.zoom_out, width=4).pack(side=tk.LEFT)
        self.zoom_label = ttk.Label(zoom_frame, text="100%", width=6)
        self.zoom_label.pack(side=tk.LEFT, padx=5)
        ttk.Button(zoom_frame, text="🔍+", command=self.zoom_in, width=4).pack(side=tk.LEFT)
        ttk.Button(zoom_frame, text="🎯", command=self.zoom_fit, width=4).pack(side=tk.LEFT, padx=(5, 0))

        # Boutons d'action
        action_frame = ttk.Frame(toolbar)
        action_frame.pack(side=tk.RIGHT, padx=10)

        ttk.Button(action_frame, text="🎨 Générer",
                  command=self.generate_preview, style='Primary.TButton').pack(side=tk.LEFT, padx=2)
        ttk.Button(action_frame, text="💾 Exporter",
                  command=self.export_poster, style='Primary.TButton').pack(side=tk.LEFT, padx=2)

    def setup_main_preview_area(self, parent):
        """Zone principale d'aperçu."""
        preview_frame = ttk.LabelFrame(parent, text="", padding="5")
        preview_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))

        # Canvas avec scrollbars
        canvas_frame = ttk.Frame(preview_frame)
        canvas_frame.pack(fill=tk.BOTH, expand=True)

        self.preview_canvas = tk.Canvas(canvas_frame, bg='white', relief='sunken', bd=1)

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=self.preview_canvas.yview)
        h_scrollbar = ttk.Scrollbar(canvas_frame, orient="horizontal", command=self.preview_canvas.xview)

        self.preview_canvas.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Grid layout
        canvas_frame.columnconfigure(0, weight=1)
        canvas_frame.rowconfigure(0, weight=1)

        self.preview_canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))

        # Bind events
        self.preview_canvas.bind("<Button-1>", self.on_canvas_click)
        self.preview_canvas.bind("<MouseWheel>", self.on_mouse_wheel)

    def setup_quick_customization_panel(self, parent):
        """Panel de personnalisation rapide intégré."""
        custom_frame = ttk.LabelFrame(parent, text="🎨 Personnalisation Rapide", padding="10")
        custom_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))

        # Scroll frame pour la personnalisation
        canvas = tk.Canvas(custom_frame, width=300)
        scrollbar = ttk.Scrollbar(custom_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Contenu de personnalisation
        self.setup_color_customization(scrollable_frame)
        self.setup_layout_customization(scrollable_frame)
        self.setup_content_customization(scrollable_frame)
        self.setup_options_customization(scrollable_frame)

        # Boutons d'action rapide
        self.setup_quick_actions(scrollable_frame)

    def setup_quick_actions(self, parent):
        """Section des actions rapides."""
        actions_frame = ttk.LabelFrame(parent, text="⚡ Actions Rapides", padding="10")
        actions_frame.pack(fill=tk.X, pady=5)

        # Boutons d'action
        ttk.Button(actions_frame, text="🎨 Générer Aperçu",
                  command=self.generate_preview, style='Primary.TButton').pack(fill=tk.X, pady=2)
        ttk.Button(actions_frame, text="💾 Exporter PNG",
                  command=self.export_poster, style='Primary.TButton').pack(fill=tk.X, pady=2)
        ttk.Button(actions_frame, text="📊 Analyser CSV",
                  command=self.analyze_csv, style='Primary.TButton').pack(fill=tk.X, pady=2)

        # Presets rapides
        presets_frame = ttk.Frame(actions_frame)
        presets_frame.pack(fill=tk.X, pady=5)

        ttk.Label(presets_frame, text="Presets:", font=('Segoe UI', 9, 'bold')).pack(anchor=tk.W)

        preset_buttons = ttk.Frame(presets_frame)
        preset_buttons.pack(fill=tk.X)

        ttk.Button(preset_buttons, text="🏫 École", command=lambda: self.apply_preset("school"),
                  width=8).pack(side=tk.LEFT, padx=2)
        ttk.Button(preset_buttons, text="🎓 Diplôme", command=lambda: self.apply_preset("graduation"),
                  width=8).pack(side=tk.LEFT, padx=2)
        ttk.Button(preset_buttons, text="🏆 Sport", command=lambda: self.apply_preset("sport"),
                  width=8).pack(side=tk.LEFT, padx=2)

    def apply_preset(self, preset_type):
        """Applique un preset de configuration."""
        presets = {
            "school": {
                "background_color": "#E8F4FD",
                "title_color": "#1E3A8A",
                "text_color": "#374151",
                "accent_color": "#3B82F6"
            },
            "graduation": {
                "background_color": "#FEF3C7",
                "title_color": "#92400E",
                "text_color": "#451A03",
                "accent_color": "#F59E0B"
            },
            "sport": {
                "background_color": "#DCFCE7",
                "title_color": "#166534",
                "text_color": "#14532D",
                "accent_color": "#22C55E"
            }
        }

        if preset_type in presets:
            preset = presets[preset_type]
            self.background_color.set(preset["background_color"])
            self.title_color.set(preset["title_color"])
            self.text_color.set(preset["text_color"])
            self.accent_color.set(preset["accent_color"])

            # Mettre à jour les boutons de couleur
            self.update_color_buttons()

            # Rafraîchir l'aperçu
            self.refresh_preview()

            self.update_status(f"Preset '{preset_type}' appliqué")

    def update_color_buttons(self):
        """Met à jour l'affichage des boutons de couleur."""
        # Cette fonction sera appelée pour mettre à jour visuellement les boutons
        # après un changement de preset
        pass

    def save_configuration(self):
        """Sauvegarde la configuration actuelle."""
        config = {
            "background_color": self.background_color.get(),
            "title_color": self.title_color.get(),
            "text_color": self.text_color.get(),
            "accent_color": self.accent_color.get(),
            "poster_width": self.poster_width.get(),
            "poster_height": self.poster_height.get(),
            "photo_size": self.photo_size.get(),
            "students_per_row": self.students_per_row.get(),
            "title_font_size": self.title_font_size.get(),
            "name_font_size": self.name_font_size.get(),
            "show_numbers": self.show_numbers.get(),
            "show_podium": self.show_podium.get(),
            "show_decorations": self.show_decorations.get(),
            "auto_height": self.auto_height.get(),
            "podium_spacing": self.podium_spacing.get(),
            "regular_spacing_x": self.regular_spacing_x.get(),
            "regular_spacing_y": self.regular_spacing_y.get()
        }

        filename = filedialog.asksaveasfilename(
            title="Sauvegarder la configuration",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if filename:
            try:
                import json
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)
                messagebox.showinfo("Succès", f"Configuration sauvegardée:\n{filename}")
                self.update_status(f"Configuration sauvegardée: {os.path.basename(filename)}")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde:\n{str(e)}")

    def load_configuration(self):
        """Charge une configuration sauvegardée."""
        filename = filedialog.askopenfilename(
            title="Charger une configuration",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if filename:
            try:
                import json
                with open(filename, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # Appliquer la configuration
                self.background_color.set(config.get("background_color", "#FFD700"))
                self.title_color.set(config.get("title_color", "#2C3E50"))
                self.text_color.set(config.get("text_color", "#2C3E50"))
                self.accent_color.set(config.get("accent_color", "#F1C40F"))
                self.poster_width.set(config.get("poster_width", 1920))
                self.poster_height.set(config.get("poster_height", 1080))
                self.photo_size.set(config.get("photo_size", 120))
                self.students_per_row.set(config.get("students_per_row", 8))
                self.title_font_size.set(config.get("title_font_size", 52))
                self.name_font_size.set(config.get("name_font_size", 22))
                self.show_numbers.set(config.get("show_numbers", True))
                self.show_podium.set(config.get("show_podium", True))
                self.show_decorations.set(config.get("show_decorations", True))
                self.auto_height.set(config.get("auto_height", True))
                self.podium_spacing.set(config.get("podium_spacing", 350))
                self.regular_spacing_x.set(config.get("regular_spacing_x", 180))
                self.regular_spacing_y.set(config.get("regular_spacing_y", 200))

                # Mettre à jour l'affichage
                self.update_color_buttons()
                self.refresh_preview()

                messagebox.showinfo("Succès", f"Configuration chargée:\n{filename}")
                self.update_status(f"Configuration chargée: {os.path.basename(filename)}")

            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors du chargement:\n{str(e)}")

    def reset_to_defaults(self):
        """Remet les paramètres par défaut."""
        if messagebox.askyesno("Confirmation", "Remettre tous les paramètres par défaut ?"):
            # Remettre les valeurs par défaut
            self.background_color.set("#FFD700")
            self.title_color.set("#2C3E50")
            self.text_color.set("#2C3E50")
            self.accent_color.set("#F1C40F")
            self.poster_width.set(1920)
            self.poster_height.set(1080)
            self.photo_size.set(120)
            self.students_per_row.set(8)
            self.title_font_size.set(52)
            self.name_font_size.set(22)
            self.show_numbers.set(True)
            self.show_podium.set(True)
            self.show_decorations.set(True)
            self.auto_height.set(True)
            self.podium_spacing.set(350)
            self.regular_spacing_x.set(180)
            self.regular_spacing_y.set(200)

            # Mettre à jour l'affichage
            self.update_color_buttons()
            self.refresh_preview()

            self.update_status("Paramètres remis par défaut")

    def setup_color_customization(self, parent):
        """Section de personnalisation des couleurs."""
        color_frame = ttk.LabelFrame(parent, text="🎨 Couleurs", padding="10")
        color_frame.pack(fill=tk.X, pady=5)

        colors = [
            ("Fond", self.background_color, "#FFD700"),
            ("Titre", self.title_color, "#2C3E50"),
            ("Texte", self.text_color, "#2C3E50"),
            ("Accent", self.accent_color, "#F1C40F")
        ]

        for label, variable, default in colors:
            self.create_color_picker(color_frame, label, variable, default)

    def create_color_picker(self, parent, label, variable, default):
        """Crée un sélecteur de couleur."""
        frame = ttk.Frame(parent)
        frame.pack(fill=tk.X, pady=2)

        ttk.Label(frame, text=label, width=8).pack(side=tk.LEFT)

        # Bouton de couleur
        color_button = tk.Button(frame, width=3, height=1,
                               bg=variable.get() or default,
                               command=lambda: self.choose_color(variable, color_button))
        color_button.pack(side=tk.LEFT, padx=5)

        # Entry pour la valeur hex
        entry = ttk.Entry(frame, textvariable=variable, width=10)
        entry.pack(side=tk.LEFT, padx=5)
        entry.bind('<KeyRelease>', lambda e: self.on_color_change(variable, color_button))

    def choose_color(self, variable, button):
        """Ouvre le sélecteur de couleur."""
        color = colorchooser.askcolor(color=variable.get())
        if color[1]:
            variable.set(color[1])
            button.config(bg=color[1])
            self.refresh_preview()

    def on_color_change(self, variable, button):
        """Appelé quand la couleur change dans l'entry."""
        try:
            color = variable.get()
            if color.startswith('#') and len(color) == 7:
                button.config(bg=color)
                self.refresh_preview()
        except:
            pass

    # Méthodes de navigation de fichiers
    def browse_csv(self):
        filename = filedialog.askopenfilename(
            title="Sélectionner le fichier CSV",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        if filename:
            self.csv_file.set(filename)
            self.analyze_csv()

    def browse_template(self):
        filename = filedialog.askopenfilename(
            title="Sélectionner le template",
            filetypes=[("Images", "*.png *.jpg *.jpeg"), ("All files", "*.*")]
        )
        if filename:
            self.template_file.set(filename)

    def browse_header(self):
        filename = filedialog.askopenfilename(
            title="Sélectionner l'image d'en-tête",
            filetypes=[("Images", "*.png *.jpg *.jpeg"), ("All files", "*.*")]
        )
        if filename:
            self.header_image.set(filename)

    def browse_footer(self):
        filename = filedialog.askopenfilename(
            title="Sélectionner l'image de pied de page",
            filetypes=[("Images", "*.png *.jpg *.jpeg"), ("All files", "*.*")]
        )
        if filename:
            self.footer_image.set(filename)

    def browse_output(self):
        dirname = filedialog.askdirectory(title="Sélectionner le dossier de sortie")
        if dirname:
            self.output_dir.set(dirname)

    def setup_status_bar(self):
        """Configure la barre d'état."""
        self.status_bar = ttk.Frame(self.root, style='Card.TFrame')
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=(0, 10))

        self.status_label = ttk.Label(self.status_bar, text="Prêt")
        self.status_label.pack(side=tk.LEFT, padx=10)

        # Barre de progression
        self.progress = ttk.Progressbar(self.status_bar, mode='indeterminate')
        self.progress.pack(side=tk.RIGHT, padx=10)

    def setup_layout_customization(self, parent):
        """Section de personnalisation de la mise en page."""
        layout_frame = ttk.LabelFrame(parent, text="📐 Mise en Page", padding="10")
        layout_frame.pack(fill=tk.X, pady=5)

        # Dimensions
        dim_frame = ttk.Frame(layout_frame)
        dim_frame.pack(fill=tk.X, pady=2)

        ttk.Label(dim_frame, text="Dimensions:", font=('Segoe UI', 9, 'bold')).pack(anchor=tk.W)

        size_frame = ttk.Frame(dim_frame)
        size_frame.pack(fill=tk.X)

        ttk.Label(size_frame, text="L:", width=2).pack(side=tk.LEFT)
        width_spin = ttk.Spinbox(size_frame, from_=800, to=4000, textvariable=self.poster_width, width=6)
        width_spin.pack(side=tk.LEFT, padx=2)
        width_spin.bind('<KeyRelease>', lambda e: self.on_dimension_change())

        ttk.Label(size_frame, text="H:", width=2).pack(side=tk.LEFT, padx=(10, 0))
        height_spin = ttk.Spinbox(size_frame, from_=600, to=3000, textvariable=self.poster_height, width=6)
        height_spin.pack(side=tk.LEFT, padx=2)
        height_spin.bind('<KeyRelease>', lambda e: self.on_dimension_change())

        # Orientation rapide
        orient_frame = ttk.Frame(layout_frame)
        orient_frame.pack(fill=tk.X, pady=5)

        ttk.Radiobutton(orient_frame, text="📱 Portrait", variable=self.orientation,
                       value="portrait", command=self.on_orientation_change).pack(side=tk.LEFT)
        ttk.Radiobutton(orient_frame, text="🖥️ Paysage", variable=self.orientation,
                       value="paysage", command=self.on_orientation_change).pack(side=tk.LEFT, padx=(10, 0))

        # Espacement
        spacing_frame = ttk.Frame(layout_frame)
        spacing_frame.pack(fill=tk.X, pady=5)

        ttk.Label(spacing_frame, text="Élèves/ligne:", width=12).pack(side=tk.LEFT)
        ttk.Spinbox(spacing_frame, from_=3, to=15, textvariable=self.students_per_row,
                   width=4, command=self.refresh_preview).pack(side=tk.LEFT)

    def setup_content_customization(self, parent):
        """Section de personnalisation du contenu."""
        content_frame = ttk.LabelFrame(parent, text="📝 Contenu", padding="10")
        content_frame.pack(fill=tk.X, pady=5)

        # Nom de classe
        class_frame = ttk.Frame(content_frame)
        class_frame.pack(fill=tk.X, pady=2)

        ttk.Label(class_frame, text="Classe:", width=8).pack(side=tk.LEFT)
        class_entry = ttk.Entry(class_frame, textvariable=self.class_name, width=15)
        class_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        class_entry.bind('<KeyRelease>', lambda e: self.refresh_preview())

        # École
        school_frame = ttk.Frame(content_frame)
        school_frame.pack(fill=tk.X, pady=2)

        ttk.Label(school_frame, text="École:", width=8).pack(side=tk.LEFT)
        school_entry = ttk.Entry(school_frame, textvariable=self.school_name, width=15)
        school_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        school_entry.bind('<KeyRelease>', lambda e: self.refresh_preview())

    def setup_options_customization(self, parent):
        """Section des options avancées."""
        options_frame = ttk.LabelFrame(parent, text="⚙️ Options", padding="10")
        options_frame.pack(fill=tk.X, pady=5)

        # Checkboxes pour les options
        options = [
            ("Numéros sur chapeaux", self.show_numbers),
            ("Podium spécial", self.show_podium),
            ("Décorations", self.show_decorations),
            ("Hauteur automatique", self.auto_height)
        ]

        for text, variable in options:
            cb = ttk.Checkbutton(options_frame, text=text, variable=variable,
                               command=self.refresh_preview)
            cb.pack(anchor=tk.W, pady=1)

        # Taille des photos
        photo_frame = ttk.Frame(options_frame)
        photo_frame.pack(fill=tk.X, pady=5)

        ttk.Label(photo_frame, text="Taille photo:", width=12).pack(side=tk.LEFT)
        ttk.Spinbox(photo_frame, from_=80, to=200, textvariable=self.photo_size,
                   width=6, command=self.refresh_preview).pack(side=tk.LEFT)

    def on_dimension_change(self):
        """Appelé quand les dimensions changent."""
        self.root.after(500, self.refresh_preview)  # Délai pour éviter trop d'appels

    def on_orientation_change(self):
        """Appelé quand l'orientation change."""
        if self.orientation.get() == "portrait":
            # Échanger largeur et hauteur
            w, h = self.poster_width.get(), self.poster_height.get()
            if w > h:
                self.poster_width.set(h)
                self.poster_height.set(w)
        else:  # paysage
            w, h = self.poster_width.get(), self.poster_height.get()
            if h > w:
                self.poster_width.set(h)
                self.poster_height.set(w)

        self.refresh_preview()

    def analyze_csv(self):
        """Analyse le fichier CSV et affiche les informations."""
        if not self.csv_file.get() or not os.path.exists(self.csv_file.get()):
            messagebox.showwarning("Attention", "Veuillez sélectionner un fichier CSV valide.")
            return

        try:
            df = pd.read_csv(self.csv_file.get())

            # Afficher les informations dans une popup
            info_window = tk.Toplevel(self.root)
            info_window.title("📊 Analyse du CSV")
            info_window.geometry("500x400")

            # Texte d'information
            text_widget = tk.Text(info_window, wrap=tk.WORD, padx=10, pady=10)
            text_widget.pack(fill=tk.BOTH, expand=True)

            info = f"📊 Analyse du fichier CSV\n"
            info += f"=" * 30 + "\n\n"
            info += f"📁 Fichier: {os.path.basename(self.csv_file.get())}\n"
            info += f"👥 Nombre d'élèves: {len(df)}\n"
            info += f"📋 Colonnes: {', '.join(df.columns)}\n\n"

            if 'Note' in df.columns:
                info += f"📈 Statistiques des notes:\n"
                info += f"   • Moyenne: {df['Note'].mean():.2f}/20\n"
                info += f"   • Médiane: {df['Note'].median():.2f}/20\n"
                info += f"   • Min: {df['Note'].min()}/20\n"
                info += f"   • Max: {df['Note'].max()}/20\n\n"

            if 'Mention' in df.columns:
                mentions = df['Mention'].value_counts()
                info += f"🏆 Répartition des mentions:\n"
                for mention, count in mentions.items():
                    info += f"   • {mention}: {count} élève(s)\n"
                info += "\n"

            info += f"👑 Podium:\n"
            for i in range(min(3, len(df))):
                student = df.iloc[i]
                info += f"   {i+1}. {student['Nom']} - {student.get('Note', 'N/A')}/20\n"

            text_widget.insert(tk.END, info)
            text_widget.config(state=tk.DISABLED)

            # Bouton fermer
            ttk.Button(info_window, text="Fermer",
                      command=info_window.destroy).pack(pady=10)

            self.update_status(f"CSV analysé: {len(df)} élèves")

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'analyse du CSV:\n{str(e)}")

    def generate_preview(self):
        """Génère l'aperçu de l'affiche."""
        if not self.validate_inputs():
            return

        self.update_status("Génération de l'aperçu...")
        self.progress.start()

        # Générer dans un thread séparé
        thread = threading.Thread(target=self._generate_preview_thread)
        thread.daemon = True
        thread.start()

    def _generate_preview_thread(self):
        """Génère l'aperçu dans un thread séparé."""
        try:
            # Créer le générateur avec configuration personnalisée
            self.generator = CongratulationsPoster(
                template_path=self.template_file.get(),
                output_dir=self.output_dir.get(),
                header_image=self.header_image.get() if self.header_image.get() else None,
                footer_image=self.footer_image.get() if self.footer_image.get() else None
            )

            # Appliquer la configuration personnalisée
            self.apply_custom_configuration()

            # Charger les données
            df = self.generator.load_students_data(self.csv_file.get())
            if df.empty:
                raise Exception("Impossible de charger les données CSV")

            # Générer l'affiche
            if self.auto_height.get():
                self.generator.poster_height = self.generator.calculate_dynamic_height(len(df))
            else:
                self.generator.poster_height = self.poster_height.get()

            self.generator.poster_width = self.poster_width.get()

            # Créer l'affiche
            poster = self.generator.create_colored_background()

            # Ajouter les images d'en-tête et de pied de page
            self.generator.add_header_image(poster)
            self.generator.add_footer_image(poster)

            draw = ImageDraw.Draw(poster)

            # Ajouter le titre
            self.generator.add_title(draw, self.class_name.get())

            # Calculer les positions et ajouter les élèves
            positions = self.generator.calculate_positions(len(df))
            for idx, (_, student) in enumerate(df.iterrows()):
                if idx < len(positions):
                    x, y = positions[idx]
                    rank = student['Classement']
                    self.generator.add_student_to_poster(poster, draw, student, x, y, rank)

            # Sauvegarder temporairement
            temp_output = os.path.join(self.output_dir.get(), "temp_preview.png")
            poster.save(temp_output, 'PNG')
            self.current_poster = poster

            # Mettre à jour l'aperçu dans le thread principal
            self.root.after(0, self._update_preview, temp_output)

        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("Erreur", f"Erreur lors de la génération:\n{str(e)}"))
        finally:
            self.root.after(0, self.progress.stop)

    def apply_custom_configuration(self):
        """Applique la configuration personnalisée au générateur."""
        if not self.generator:
            return

        # Couleurs
        self.generator.colors.update({
            'background': self.background_color.get(),
            'text_primary': self.title_color.get(),
            'text_secondary': self.text_color.get(),
            'gold': self.accent_color.get()
        })

        # Mise en page
        self.generator.layout_config.update({
            'podium_spacing': self.podium_spacing.get(),
            'regular_spacing_x': self.regular_spacing_x.get(),
            'regular_spacing_y': self.regular_spacing_y.get()
        })

        # Taille des photos
        self.generator.photo_size = self.photo_size.get()

    def validate_inputs(self):
        """Valide les entrées utilisateur."""
        if not self.csv_file.get():
            messagebox.showwarning("Attention", "Veuillez sélectionner un fichier CSV.")
            return False

        if not os.path.exists(self.csv_file.get()):
            messagebox.showerror("Erreur", "Le fichier CSV n'existe pas.")
            return False

        if not self.class_name.get().strip():
            messagebox.showwarning("Attention", "Veuillez entrer un nom de classe.")
            return False

        return True

    def _update_preview(self, image_path):
        """Met à jour l'aperçu dans l'interface."""
        try:
            # Charger l'image
            image = Image.open(image_path)
            self.original_image = image.copy()

            # Appliquer le zoom
            self.update_preview_zoom()

            # Supprimer le fichier temporaire
            if os.path.exists(image_path):
                os.remove(image_path)

            self.update_status("Aperçu généré avec succès")

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'affichage:\n{str(e)}")
            self.update_status("Erreur lors de l'affichage")

    def update_preview_zoom(self):
        """Met à jour l'affichage avec le zoom actuel."""
        if not hasattr(self, 'original_image') or not self.original_image:
            return

        # Calculer la nouvelle taille
        new_width = int(self.original_image.width * self.zoom_factor)
        new_height = int(self.original_image.height * self.zoom_factor)

        # Redimensionner l'image
        zoomed_image = self.original_image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        self.preview_image = ImageTk.PhotoImage(zoomed_image)

        # Effacer et afficher
        self.preview_canvas.delete("all")
        self.preview_canvas.create_image(
            new_width // 2, new_height // 2,
            image=self.preview_image,
            anchor=tk.CENTER
        )

        # Configurer la zone de défilement
        self.preview_canvas.configure(scrollregion=(0, 0, new_width, new_height))

        # Mettre à jour le label de zoom
        zoom_percent = int(self.zoom_factor * 100)
        self.zoom_label.config(text=f"{zoom_percent}%")

    def zoom_in(self):
        """Zoom avant."""
        self.zoom_factor = min(3.0, self.zoom_factor * 1.2)
        self.update_preview_zoom()

    def zoom_out(self):
        """Zoom arrière."""
        self.zoom_factor = max(0.1, self.zoom_factor / 1.2)
        self.update_preview_zoom()

    def zoom_fit(self):
        """Ajuste le zoom pour que l'image soit entièrement visible."""
        if not hasattr(self, 'original_image') or not self.original_image:
            return

        canvas_width = self.preview_canvas.winfo_width()
        canvas_height = self.preview_canvas.winfo_height()

        if canvas_width > 1 and canvas_height > 1:
            zoom_x = canvas_width / self.original_image.width
            zoom_y = canvas_height / self.original_image.height
            self.zoom_factor = min(zoom_x, zoom_y) * 0.9  # 90% pour laisser une marge
            self.update_preview_zoom()

    def on_canvas_click(self, event):
        """Gère les clics sur le canvas."""
        # Ici on pourrait ajouter des fonctionnalités d'édition directe
        pass

    def on_mouse_wheel(self, event):
        """Gère la molette de la souris pour le zoom."""
        if event.state & 0x4:  # Ctrl + molette = zoom
            if event.delta > 0:
                self.zoom_in()
            else:
                self.zoom_out()
        else:
            # Défilement normal
            self.preview_canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")

    def refresh_preview(self):
        """Actualise l'aperçu."""
        if hasattr(self, 'original_image') and self.original_image:
            self.generate_preview()

    def export_poster(self):
        """Exporte l'affiche finale."""
        if not self.current_poster:
            messagebox.showwarning("Attention", "Veuillez d'abord générer un aperçu.")
            return

        filename = filedialog.asksaveasfilename(
            title="Exporter l'affiche",
            defaultextension=".png",
            filetypes=[("PNG files", "*.png"), ("JPEG files", "*.jpg"), ("All files", "*.*")]
        )

        if filename:
            try:
                self.current_poster.save(filename)
                messagebox.showinfo("Succès", f"Affiche exportée avec succès:\n{filename}")
                self.update_status(f"Affiche exportée: {os.path.basename(filename)}")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de l'export:\n{str(e)}")

    def load_default_settings(self):
        """Charge les paramètres par défaut."""
        self.update_status("Interface prête")

    def update_status(self, message):
        """Met à jour la barre d'état."""
        self.status_label.config(text=message)
        self.root.update_idletasks()

    def run(self):
        """Lance l'application."""
        self.root.mainloop()

def main():
    """Fonction principale."""
    app = ModernPosterApp()
    app.run()

if __name__ == "__main__":
    main()
