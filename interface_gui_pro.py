#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Interface graphique professionnelle moderne pour le générateur d'affiches
Avec personnalisation avancée et aperçu en temps réel
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, colorchooser
import threading
import os
from PIL import Image, ImageTk, ImageDraw, ImageFont
import pandas as pd
from congratulations_poster import CongratulationsPoster
import json

class ModernPosterApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎓 Générateur d'Affiches Professionnel")
        self.root.geometry("1600x1000")
        self.root.state('zoomed')

        # Variables de configuration
        self.setup_variables()

        # Style moderne
        self.setup_modern_theme()

        # Interface utilisateur
        self.setup_professional_ui()

        # Générateur et aperçu
        self.generator = None
        self.current_poster = None
        self.preview_image = None
        self.zoom_factor = 1.0

        # Chargement initial
        self.load_default_settings()

    def setup_variables(self):
        """Initialise toutes les variables de configuration."""
        # Fichiers
        self.csv_file = tk.StringVar()
        self.template_file = tk.StringVar(value="templates/fond_defaut.png")
        self.header_image = tk.StringVar()
        self.footer_image = tk.StringVar()
        self.output_dir = tk.StringVar(value="output")

        # Contenu
        self.class_name = tk.StringVar(value="Ma Classe")
        self.school_name = tk.StringVar(value="École Exemple")
        self.academic_year = tk.StringVar(value="2024-2025")

        # Mise en page
        self.orientation = tk.StringVar(value="paysage")
        self.poster_width = tk.IntVar(value=1920)
        self.poster_height = tk.IntVar(value=1080)
        self.photo_size = tk.IntVar(value=120)
        self.students_per_row = tk.IntVar(value=8)

        # Style et couleurs
        self.background_color = tk.StringVar(value="#FFD700")
        self.title_color = tk.StringVar(value="#2C3E50")
        self.text_color = tk.StringVar(value="#2C3E50")
        self.accent_color = tk.StringVar(value="#F1C40F")

        # Polices
        self.title_font_size = tk.IntVar(value=52)
        self.name_font_size = tk.IntVar(value=22)
        self.info_font_size = tk.IntVar(value=16)

        # Options avancées
        self.show_numbers = tk.BooleanVar(value=True)
        self.show_podium = tk.BooleanVar(value=True)
        self.show_decorations = tk.BooleanVar(value=True)
        self.auto_height = tk.BooleanVar(value=True)

        # Espacement
        self.podium_spacing = tk.IntVar(value=350)
        self.regular_spacing_x = tk.IntVar(value=180)
        self.regular_spacing_y = tk.IntVar(value=200)

    def setup_modern_theme(self):
        """Configure le thème moderne professionnel."""
        style = ttk.Style()
        style.theme_use('clam')

        # Couleurs du thème
        self.theme_colors = {
            'primary': '#2C3E50',
            'secondary': '#3498DB',
            'accent': '#E74C3C',
            'success': '#27AE60',
            'warning': '#F39C12',
            'background': '#F8F9FA',
            'surface': '#FFFFFF',
            'text': '#2C3E50',
            'text_light': '#7F8C8D',
            'border': '#E9ECEF'
        }

        # Configuration des styles
        style.configure('Title.TLabel',
                       font=('Segoe UI', 18, 'bold'),
                       foreground=self.theme_colors['primary'],
                       background=self.theme_colors['background'])

        style.configure('Heading.TLabel',
                       font=('Segoe UI', 12, 'bold'),
                       foreground=self.theme_colors['text'])

        style.configure('Modern.TFrame',
                       background=self.theme_colors['surface'],
                       relief='flat')

        style.configure('Card.TFrame',
                       background=self.theme_colors['surface'],
                       relief='solid',
                       borderwidth=1)

        style.configure('Primary.TButton',
                       font=('Segoe UI', 10, 'bold'))

        style.map('Primary.TButton',
                 background=[('active', self.theme_colors['secondary']),
                           ('!active', self.theme_colors['primary'])],
                 foreground=[('active', 'white'), ('!active', 'white')])

    def setup_professional_ui(self):
        """Configure l'interface utilisateur professionnelle."""
        # Conteneur principal
        main_container = ttk.Frame(self.root, style='Modern.TFrame')
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Configuration du grid principal
        main_container.columnconfigure(0, weight=1, minsize=400)  # Panel de contrôle
        main_container.columnconfigure(1, weight=2, minsize=800)  # Panel d'aperçu
        main_container.rowconfigure(0, weight=1)

        # Panel de contrôle gauche
        self.setup_control_panel(main_container)

        # Panel d'aperçu droite avec personnalisation intégrée
        self.setup_integrated_preview_panel(main_container)

        # Barre d'état
        self.setup_status_bar()

    def setup_control_panel(self, parent):
        """Configure le panel de contrôle avec onglets."""
        control_frame = ttk.LabelFrame(parent, text="⚙️ Configuration", padding="10")
        control_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))

        # Notebook pour les onglets
        notebook = ttk.Notebook(control_frame)
        notebook.pack(fill=tk.BOTH, expand=True)

        # Onglets
        self.setup_files_tab(notebook)
        self.setup_content_tab(notebook)
        self.setup_layout_tab(notebook)
        self.setup_style_tab(notebook)
        self.setup_advanced_tab(notebook)

    def setup_content_tab(self, notebook):
        """Onglet de contenu."""
        content_frame = ttk.Frame(notebook)
        notebook.add(content_frame, text="📝 Contenu")

        # Scroll frame
        canvas = tk.Canvas(content_frame)
        scrollbar = ttk.Scrollbar(content_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Contenu
        self.create_text_section(scrollable_frame, "🏫 Nom de classe", self.class_name)
        self.create_text_section(scrollable_frame, "🎓 École", self.school_name)
        self.create_text_section(scrollable_frame, "📅 Année scolaire", self.academic_year)

    def setup_layout_tab(self, notebook):
        """Onglet de mise en page."""
        layout_frame = ttk.Frame(notebook)
        notebook.add(layout_frame, text="📐 Layout")

        # Scroll frame
        canvas = tk.Canvas(layout_frame)
        scrollbar = ttk.Scrollbar(layout_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Dimensions
        dim_frame = ttk.LabelFrame(scrollable_frame, text="📏 Dimensions", padding="10")
        dim_frame.pack(fill=tk.X, pady=5)

        # Largeur
        width_frame = ttk.Frame(dim_frame)
        width_frame.pack(fill=tk.X, pady=2)
        ttk.Label(width_frame, text="Largeur:", width=12).pack(side=tk.LEFT)
        ttk.Spinbox(width_frame, from_=800, to=4000, textvariable=self.poster_width,
                   width=8, command=self.refresh_preview).pack(side=tk.LEFT)

        # Hauteur
        height_frame = ttk.Frame(dim_frame)
        height_frame.pack(fill=tk.X, pady=2)
        ttk.Label(height_frame, text="Hauteur:", width=12).pack(side=tk.LEFT)
        ttk.Spinbox(height_frame, from_=600, to=3000, textvariable=self.poster_height,
                   width=8, command=self.refresh_preview).pack(side=tk.LEFT)

        # Orientation
        orient_frame = ttk.LabelFrame(scrollable_frame, text="🔄 Orientation", padding="10")
        orient_frame.pack(fill=tk.X, pady=5)

        ttk.Radiobutton(orient_frame, text="📱 Portrait", variable=self.orientation,
                       value="portrait", command=self.on_orientation_change).pack(anchor=tk.W)
        ttk.Radiobutton(orient_frame, text="🖥️ Paysage", variable=self.orientation,
                       value="paysage", command=self.on_orientation_change).pack(anchor=tk.W)

        # Espacement
        spacing_frame = ttk.LabelFrame(scrollable_frame, text="📐 Espacement", padding="10")
        spacing_frame.pack(fill=tk.X, pady=5)

        # Élèves par ligne
        students_frame = ttk.Frame(spacing_frame)
        students_frame.pack(fill=tk.X, pady=2)
        ttk.Label(students_frame, text="Élèves/ligne:", width=12).pack(side=tk.LEFT)
        ttk.Spinbox(students_frame, from_=3, to=15, textvariable=self.students_per_row,
                   width=4, command=self.refresh_preview).pack(side=tk.LEFT)

        # Taille des photos
        photo_frame = ttk.Frame(spacing_frame)
        photo_frame.pack(fill=tk.X, pady=2)
        ttk.Label(photo_frame, text="Taille photo:", width=12).pack(side=tk.LEFT)
        ttk.Spinbox(photo_frame, from_=80, to=200, textvariable=self.photo_size,
                   width=4, command=self.refresh_preview).pack(side=tk.LEFT)

    def setup_style_tab(self, notebook):
        """Onglet de style."""
        style_frame = ttk.Frame(notebook)
        notebook.add(style_frame, text="🎨 Style")

        # Scroll frame
        canvas = tk.Canvas(style_frame)
        scrollbar = ttk.Scrollbar(style_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Couleurs
        color_frame = ttk.LabelFrame(scrollable_frame, text="🎨 Couleurs", padding="10")
        color_frame.pack(fill=tk.X, pady=5)

        colors = [
            ("Fond", self.background_color, "#FFD700"),
            ("Titre", self.title_color, "#2C3E50"),
            ("Texte", self.text_color, "#2C3E50"),
            ("Accent", self.accent_color, "#F1C40F")
        ]

        for label, variable, default in colors:
            self.create_color_picker(color_frame, label, variable, default)

        # Polices
        font_frame = ttk.LabelFrame(scrollable_frame, text="🔤 Polices", padding="10")
        font_frame.pack(fill=tk.X, pady=5)

        # Taille titre
        title_font_frame = ttk.Frame(font_frame)
        title_font_frame.pack(fill=tk.X, pady=2)
        ttk.Label(title_font_frame, text="Titre:", width=12).pack(side=tk.LEFT)
        ttk.Spinbox(title_font_frame, from_=24, to=72, textvariable=self.title_font_size,
                   width=4, command=self.refresh_preview).pack(side=tk.LEFT)

        # Taille nom
        name_font_frame = ttk.Frame(font_frame)
        name_font_frame.pack(fill=tk.X, pady=2)
        ttk.Label(name_font_frame, text="Nom:", width=12).pack(side=tk.LEFT)
        ttk.Spinbox(name_font_frame, from_=12, to=36, textvariable=self.name_font_size,
                   width=4, command=self.refresh_preview).pack(side=tk.LEFT)

    def setup_advanced_tab(self, notebook):
        """Onglet des options avancées."""
        advanced_frame = ttk.Frame(notebook)
        notebook.add(advanced_frame, text="⚙️ Avancé")

        # Scroll frame
        canvas = tk.Canvas(advanced_frame)
        scrollbar = ttk.Scrollbar(advanced_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Options
        options_frame = ttk.LabelFrame(scrollable_frame, text="⚙️ Options", padding="10")
        options_frame.pack(fill=tk.X, pady=5)

        options = [
            ("Numéros sur chapeaux", self.show_numbers),
            ("Podium spécial", self.show_podium),
            ("Décorations", self.show_decorations),
            ("Hauteur automatique", self.auto_height)
        ]

        for text, variable in options:
            cb = ttk.Checkbutton(options_frame, text=text, variable=variable,
                               command=self.refresh_preview)
            cb.pack(anchor=tk.W, pady=1)

        # Espacement avancé
        spacing_frame = ttk.LabelFrame(scrollable_frame, text="📐 Espacement Avancé", padding="10")
        spacing_frame.pack(fill=tk.X, pady=5)

        # Podium
        podium_frame = ttk.Frame(spacing_frame)
        podium_frame.pack(fill=tk.X, pady=2)
        ttk.Label(podium_frame, text="Podium:", width=12).pack(side=tk.LEFT)
        ttk.Spinbox(podium_frame, from_=200, to=500, textvariable=self.podium_spacing,
                   width=6, command=self.refresh_preview).pack(side=tk.LEFT)

        # Horizontal
        h_spacing_frame = ttk.Frame(spacing_frame)
        h_spacing_frame.pack(fill=tk.X, pady=2)
        ttk.Label(h_spacing_frame, text="Horizontal:", width=12).pack(side=tk.LEFT)
        ttk.Spinbox(h_spacing_frame, from_=100, to=300, textvariable=self.regular_spacing_x,
                   width=6, command=self.refresh_preview).pack(side=tk.LEFT)

        # Vertical
        v_spacing_frame = ttk.Frame(spacing_frame)
        v_spacing_frame.pack(fill=tk.X, pady=2)
        ttk.Label(v_spacing_frame, text="Vertical:", width=12).pack(side=tk.LEFT)
        ttk.Spinbox(v_spacing_frame, from_=150, to=300, textvariable=self.regular_spacing_y,
                   width=6, command=self.refresh_preview).pack(side=tk.LEFT)

    def create_text_section(self, parent, label, variable):
        """Crée une section de texte."""
        frame = ttk.LabelFrame(parent, text=label, padding="5")
        frame.pack(fill=tk.X, pady=5)

        entry = ttk.Entry(frame, textvariable=variable, font=('Segoe UI', 10))
        entry.pack(fill=tk.X)
        entry.bind('<KeyRelease>', lambda e: self.refresh_preview())

    def setup_files_tab(self, notebook):
        """Onglet de gestion des fichiers."""
        files_frame = ttk.Frame(notebook)
        notebook.add(files_frame, text="📁 Fichiers")

        # Scroll frame
        canvas = tk.Canvas(files_frame)
        scrollbar = ttk.Scrollbar(files_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Contenu des fichiers
        self.create_file_section(scrollable_frame, "📊 Données CSV", self.csv_file, self.browse_csv)
        self.create_file_section(scrollable_frame, "🎨 Template", self.template_file, self.browse_template)
        self.create_file_section(scrollable_frame, "📋 En-tête", self.header_image, self.browse_header)
        self.create_file_section(scrollable_frame, "📄 Pied de page", self.footer_image, self.browse_footer)
        self.create_file_section(scrollable_frame, "💾 Sortie", self.output_dir, self.browse_output, is_dir=True)

        # Boutons d'action
        action_frame = ttk.Frame(scrollable_frame)
        action_frame.pack(fill=tk.X, pady=10)

        ttk.Button(action_frame, text="📊 Analyser CSV",
                  command=self.analyze_csv, style='Primary.TButton').pack(fill=tk.X, pady=2)
        ttk.Button(action_frame, text="🔄 Actualiser",
                  command=self.refresh_preview, style='Primary.TButton').pack(fill=tk.X, pady=2)

    def create_file_section(self, parent, label, variable, command, is_dir=False):
        """Crée une section de sélection de fichier."""
        frame = ttk.LabelFrame(parent, text=label, padding="5")
        frame.pack(fill=tk.X, pady=5)

        entry_frame = ttk.Frame(frame)
        entry_frame.pack(fill=tk.X)

        entry = ttk.Entry(entry_frame, textvariable=variable, font=('Segoe UI', 9))
        entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        ttk.Button(entry_frame, text="📂", command=command, width=3).pack(side=tk.RIGHT, padx=(5, 0))

    def setup_integrated_preview_panel(self, parent):
        """Panel d'aperçu avec personnalisation intégrée."""
        preview_container = ttk.Frame(parent)
        preview_container.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configuration du grid
        preview_container.columnconfigure(0, weight=1)
        preview_container.rowconfigure(1, weight=1)

        # Barre d'outils d'aperçu
        self.setup_preview_toolbar(preview_container)

        # Zone d'aperçu principale
        self.setup_main_preview_area(preview_container)

        # Panel de personnalisation rapide (côté droit)
        self.setup_quick_customization_panel(preview_container)

    def setup_preview_toolbar(self, parent):
        """Barre d'outils pour l'aperçu."""
        toolbar = ttk.Frame(parent, style='Card.TFrame')
        toolbar.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        # Titre
        ttk.Label(toolbar, text="👁️ Aperçu en Temps Réel",
                 style='Heading.TLabel').pack(side=tk.LEFT, padx=10)

        # Contrôles de zoom
        zoom_frame = ttk.Frame(toolbar)
        zoom_frame.pack(side=tk.RIGHT, padx=10)

        ttk.Button(zoom_frame, text="🔍-", command=self.zoom_out, width=4).pack(side=tk.LEFT)
        self.zoom_label = ttk.Label(zoom_frame, text="100%", width=6)
        self.zoom_label.pack(side=tk.LEFT, padx=5)
        ttk.Button(zoom_frame, text="🔍+", command=self.zoom_in, width=4).pack(side=tk.LEFT)
        ttk.Button(zoom_frame, text="🎯", command=self.zoom_fit, width=4).pack(side=tk.LEFT, padx=(5, 0))

        # Boutons d'action
        action_frame = ttk.Frame(toolbar)
        action_frame.pack(side=tk.RIGHT, padx=10)

        ttk.Button(action_frame, text="🎨 Générer",
                  command=self.generate_preview, style='Primary.TButton').pack(side=tk.LEFT, padx=2)
        ttk.Button(action_frame, text="💾 Exporter",
                  command=self.export_poster, style='Primary.TButton').pack(side=tk.LEFT, padx=2)

    def setup_main_preview_area(self, parent):
        """Zone principale d'aperçu."""
        preview_frame = ttk.LabelFrame(parent, text="", padding="5")
        preview_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))

        # Canvas avec scrollbars
        canvas_frame = ttk.Frame(preview_frame)
        canvas_frame.pack(fill=tk.BOTH, expand=True)

        self.preview_canvas = tk.Canvas(canvas_frame, bg='white', relief='sunken', bd=1)

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=self.preview_canvas.yview)
        h_scrollbar = ttk.Scrollbar(canvas_frame, orient="horizontal", command=self.preview_canvas.xview)

        self.preview_canvas.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Grid layout
        canvas_frame.columnconfigure(0, weight=1)
        canvas_frame.rowconfigure(0, weight=1)

        self.preview_canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))

        # Bind events
        self.preview_canvas.bind("<Button-1>", self.on_canvas_click)
        self.preview_canvas.bind("<MouseWheel>", self.on_mouse_wheel)

    def setup_quick_customization_panel(self, parent):
        """Panel de personnalisation rapide intégré."""
        custom_frame = ttk.LabelFrame(parent, text="🎨 Personnalisation Rapide", padding="10")
        custom_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))

        # Scroll frame pour la personnalisation
        canvas = tk.Canvas(custom_frame, width=300)
        scrollbar = ttk.Scrollbar(custom_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Contenu de personnalisation
        self.setup_color_customization(scrollable_frame)
        self.setup_layout_customization(scrollable_frame)
        self.setup_content_customization(scrollable_frame)
        self.setup_options_customization(scrollable_frame)

    def setup_color_customization(self, parent):
        """Section de personnalisation des couleurs."""
        color_frame = ttk.LabelFrame(parent, text="🎨 Couleurs", padding="10")
        color_frame.pack(fill=tk.X, pady=5)

        colors = [
            ("Fond", self.background_color, "#FFD700"),
            ("Titre", self.title_color, "#2C3E50"),
            ("Texte", self.text_color, "#2C3E50"),
            ("Accent", self.accent_color, "#F1C40F")
        ]

        for label, variable, default in colors:
            self.create_color_picker(color_frame, label, variable, default)

    def create_color_picker(self, parent, label, variable, default):
        """Crée un sélecteur de couleur."""
        frame = ttk.Frame(parent)
        frame.pack(fill=tk.X, pady=2)

        ttk.Label(frame, text=label, width=8).pack(side=tk.LEFT)

        # Bouton de couleur
        color_button = tk.Button(frame, width=3, height=1,
                               bg=variable.get() or default,
                               command=lambda: self.choose_color(variable, color_button))
        color_button.pack(side=tk.LEFT, padx=5)

        # Entry pour la valeur hex
        entry = ttk.Entry(frame, textvariable=variable, width=10)
        entry.pack(side=tk.LEFT, padx=5)
        entry.bind('<KeyRelease>', lambda e: self.on_color_change(variable, color_button))

    def choose_color(self, variable, button):
        """Ouvre le sélecteur de couleur."""
        color = colorchooser.askcolor(color=variable.get())
        if color[1]:
            variable.set(color[1])
            button.config(bg=color[1])
            self.refresh_preview()

    def on_color_change(self, variable, button):
        """Appelé quand la couleur change dans l'entry."""
        try:
            color = variable.get()
            if color.startswith('#') and len(color) == 7:
                button.config(bg=color)
                self.refresh_preview()
        except:
            pass

    # Méthodes de navigation de fichiers
    def browse_csv(self):
        filename = filedialog.askopenfilename(
            title="Sélectionner le fichier CSV",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        if filename:
            self.csv_file.set(filename)
            self.analyze_csv()

    def browse_template(self):
        filename = filedialog.askopenfilename(
            title="Sélectionner le template",
            filetypes=[("Images", "*.png *.jpg *.jpeg"), ("All files", "*.*")]
        )
        if filename:
            self.template_file.set(filename)

    def browse_header(self):
        filename = filedialog.askopenfilename(
            title="Sélectionner l'image d'en-tête",
            filetypes=[("Images", "*.png *.jpg *.jpeg"), ("All files", "*.*")]
        )
        if filename:
            self.header_image.set(filename)

    def browse_footer(self):
        filename = filedialog.askopenfilename(
            title="Sélectionner l'image de pied de page",
            filetypes=[("Images", "*.png *.jpg *.jpeg"), ("All files", "*.*")]
        )
        if filename:
            self.footer_image.set(filename)

    def browse_output(self):
        dirname = filedialog.askdirectory(title="Sélectionner le dossier de sortie")
        if dirname:
            self.output_dir.set(dirname)

    def setup_status_bar(self):
        """Configure la barre d'état."""
        self.status_bar = ttk.Frame(self.root, style='Card.TFrame')
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=(0, 10))

        self.status_label = ttk.Label(self.status_bar, text="Prêt")
        self.status_label.pack(side=tk.LEFT, padx=10)

        # Barre de progression
        self.progress = ttk.Progressbar(self.status_bar, mode='indeterminate')
        self.progress.pack(side=tk.RIGHT, padx=10)

    def setup_layout_customization(self, parent):
        """Section de personnalisation de la mise en page."""
        layout_frame = ttk.LabelFrame(parent, text="📐 Mise en Page", padding="10")
        layout_frame.pack(fill=tk.X, pady=5)

        # Dimensions
        dim_frame = ttk.Frame(layout_frame)
        dim_frame.pack(fill=tk.X, pady=2)

        ttk.Label(dim_frame, text="Dimensions:", font=('Segoe UI', 9, 'bold')).pack(anchor=tk.W)

        size_frame = ttk.Frame(dim_frame)
        size_frame.pack(fill=tk.X)

        ttk.Label(size_frame, text="L:", width=2).pack(side=tk.LEFT)
        width_spin = ttk.Spinbox(size_frame, from_=800, to=4000, textvariable=self.poster_width, width=6)
        width_spin.pack(side=tk.LEFT, padx=2)
        width_spin.bind('<KeyRelease>', lambda e: self.on_dimension_change())

        ttk.Label(size_frame, text="H:", width=2).pack(side=tk.LEFT, padx=(10, 0))
        height_spin = ttk.Spinbox(size_frame, from_=600, to=3000, textvariable=self.poster_height, width=6)
        height_spin.pack(side=tk.LEFT, padx=2)
        height_spin.bind('<KeyRelease>', lambda e: self.on_dimension_change())

        # Orientation rapide
        orient_frame = ttk.Frame(layout_frame)
        orient_frame.pack(fill=tk.X, pady=5)

        ttk.Radiobutton(orient_frame, text="📱 Portrait", variable=self.orientation,
                       value="portrait", command=self.on_orientation_change).pack(side=tk.LEFT)
        ttk.Radiobutton(orient_frame, text="🖥️ Paysage", variable=self.orientation,
                       value="paysage", command=self.on_orientation_change).pack(side=tk.LEFT, padx=(10, 0))

        # Espacement
        spacing_frame = ttk.Frame(layout_frame)
        spacing_frame.pack(fill=tk.X, pady=5)

        ttk.Label(spacing_frame, text="Élèves/ligne:", width=12).pack(side=tk.LEFT)
        ttk.Spinbox(spacing_frame, from_=3, to=15, textvariable=self.students_per_row,
                   width=4, command=self.refresh_preview).pack(side=tk.LEFT)

    def setup_content_customization(self, parent):
        """Section de personnalisation du contenu."""
        content_frame = ttk.LabelFrame(parent, text="📝 Contenu", padding="10")
        content_frame.pack(fill=tk.X, pady=5)

        # Nom de classe
        class_frame = ttk.Frame(content_frame)
        class_frame.pack(fill=tk.X, pady=2)

        ttk.Label(class_frame, text="Classe:", width=8).pack(side=tk.LEFT)
        class_entry = ttk.Entry(class_frame, textvariable=self.class_name, width=15)
        class_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        class_entry.bind('<KeyRelease>', lambda e: self.refresh_preview())

        # École
        school_frame = ttk.Frame(content_frame)
        school_frame.pack(fill=tk.X, pady=2)

        ttk.Label(school_frame, text="École:", width=8).pack(side=tk.LEFT)
        school_entry = ttk.Entry(school_frame, textvariable=self.school_name, width=15)
        school_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        school_entry.bind('<KeyRelease>', lambda e: self.refresh_preview())

    def setup_options_customization(self, parent):
        """Section des options avancées."""
        options_frame = ttk.LabelFrame(parent, text="⚙️ Options", padding="10")
        options_frame.pack(fill=tk.X, pady=5)

        # Checkboxes pour les options
        options = [
            ("Numéros sur chapeaux", self.show_numbers),
            ("Podium spécial", self.show_podium),
            ("Décorations", self.show_decorations),
            ("Hauteur automatique", self.auto_height)
        ]

        for text, variable in options:
            cb = ttk.Checkbutton(options_frame, text=text, variable=variable,
                               command=self.refresh_preview)
            cb.pack(anchor=tk.W, pady=1)

        # Taille des photos
        photo_frame = ttk.Frame(options_frame)
        photo_frame.pack(fill=tk.X, pady=5)

        ttk.Label(photo_frame, text="Taille photo:", width=12).pack(side=tk.LEFT)
        ttk.Spinbox(photo_frame, from_=80, to=200, textvariable=self.photo_size,
                   width=6, command=self.refresh_preview).pack(side=tk.LEFT)

    def on_dimension_change(self):
        """Appelé quand les dimensions changent."""
        self.root.after(500, self.refresh_preview)  # Délai pour éviter trop d'appels

    def on_orientation_change(self):
        """Appelé quand l'orientation change."""
        if self.orientation.get() == "portrait":
            # Échanger largeur et hauteur
            w, h = self.poster_width.get(), self.poster_height.get()
            if w > h:
                self.poster_width.set(h)
                self.poster_height.set(w)
        else:  # paysage
            w, h = self.poster_width.get(), self.poster_height.get()
            if h > w:
                self.poster_width.set(h)
                self.poster_height.set(w)

        self.refresh_preview()

    def analyze_csv(self):
        """Analyse le fichier CSV et affiche les informations."""
        if not self.csv_file.get() or not os.path.exists(self.csv_file.get()):
            messagebox.showwarning("Attention", "Veuillez sélectionner un fichier CSV valide.")
            return

        try:
            df = pd.read_csv(self.csv_file.get())

            # Afficher les informations dans une popup
            info_window = tk.Toplevel(self.root)
            info_window.title("📊 Analyse du CSV")
            info_window.geometry("500x400")

            # Texte d'information
            text_widget = tk.Text(info_window, wrap=tk.WORD, padx=10, pady=10)
            text_widget.pack(fill=tk.BOTH, expand=True)

            info = f"📊 Analyse du fichier CSV\n"
            info += f"=" * 30 + "\n\n"
            info += f"📁 Fichier: {os.path.basename(self.csv_file.get())}\n"
            info += f"👥 Nombre d'élèves: {len(df)}\n"
            info += f"📋 Colonnes: {', '.join(df.columns)}\n\n"

            if 'Note' in df.columns:
                info += f"📈 Statistiques des notes:\n"
                info += f"   • Moyenne: {df['Note'].mean():.2f}/20\n"
                info += f"   • Médiane: {df['Note'].median():.2f}/20\n"
                info += f"   • Min: {df['Note'].min()}/20\n"
                info += f"   • Max: {df['Note'].max()}/20\n\n"

            if 'Mention' in df.columns:
                mentions = df['Mention'].value_counts()
                info += f"🏆 Répartition des mentions:\n"
                for mention, count in mentions.items():
                    info += f"   • {mention}: {count} élève(s)\n"
                info += "\n"

            info += f"👑 Podium:\n"
            for i in range(min(3, len(df))):
                student = df.iloc[i]
                info += f"   {i+1}. {student['Nom']} - {student.get('Note', 'N/A')}/20\n"

            text_widget.insert(tk.END, info)
            text_widget.config(state=tk.DISABLED)

            # Bouton fermer
            ttk.Button(info_window, text="Fermer",
                      command=info_window.destroy).pack(pady=10)

            self.update_status(f"CSV analysé: {len(df)} élèves")

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'analyse du CSV:\n{str(e)}")

    def generate_preview(self):
        """Génère l'aperçu de l'affiche."""
        if not self.validate_inputs():
            return

        self.update_status("Génération de l'aperçu...")
        self.progress.start()

        # Générer dans un thread séparé
        thread = threading.Thread(target=self._generate_preview_thread)
        thread.daemon = True
        thread.start()

    def _generate_preview_thread(self):
        """Génère l'aperçu dans un thread séparé."""
        try:
            # Créer le générateur avec configuration personnalisée
            self.generator = CongratulationsPoster(
                template_path=self.template_file.get(),
                output_dir=self.output_dir.get(),
                header_image=self.header_image.get() if self.header_image.get() else None,
                footer_image=self.footer_image.get() if self.footer_image.get() else None
            )

            # Appliquer la configuration personnalisée
            self.apply_custom_configuration()

            # Charger les données
            df = self.generator.load_students_data(self.csv_file.get())
            if df.empty:
                raise Exception("Impossible de charger les données CSV")

            # Générer l'affiche
            if self.auto_height.get():
                self.generator.poster_height = self.generator.calculate_dynamic_height(len(df))
            else:
                self.generator.poster_height = self.poster_height.get()

            self.generator.poster_width = self.poster_width.get()

            # Créer l'affiche
            poster = self.generator.create_colored_background()

            # Ajouter les images d'en-tête et de pied de page
            self.generator.add_header_image(poster)
            self.generator.add_footer_image(poster)

            draw = ImageDraw.Draw(poster)

            # Ajouter le titre
            self.generator.add_title(draw, self.class_name.get())

            # Calculer les positions et ajouter les élèves
            positions = self.generator.calculate_positions(len(df))
            for idx, (_, student) in enumerate(df.iterrows()):
                if idx < len(positions):
                    x, y = positions[idx]
                    rank = student['Classement']
                    self.generator.add_student_to_poster(poster, draw, student, x, y, rank)

            # Sauvegarder temporairement
            temp_output = os.path.join(self.output_dir.get(), "temp_preview.png")
            poster.save(temp_output, 'PNG')
            self.current_poster = poster

            # Mettre à jour l'aperçu dans le thread principal
            self.root.after(0, self._update_preview, temp_output)

        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("Erreur", f"Erreur lors de la génération:\n{str(e)}"))
        finally:
            self.root.after(0, self.progress.stop)

    def apply_custom_configuration(self):
        """Applique la configuration personnalisée au générateur."""
        if not self.generator:
            return

        # Couleurs
        self.generator.colors.update({
            'background': self.background_color.get(),
            'text_primary': self.title_color.get(),
            'text_secondary': self.text_color.get(),
            'gold': self.accent_color.get()
        })

        # Mise en page
        self.generator.layout_config.update({
            'podium_spacing': self.podium_spacing.get(),
            'regular_spacing_x': self.regular_spacing_x.get(),
            'regular_spacing_y': self.regular_spacing_y.get()
        })

        # Taille des photos
        self.generator.photo_size = self.photo_size.get()

    def validate_inputs(self):
        """Valide les entrées utilisateur."""
        if not self.csv_file.get():
            messagebox.showwarning("Attention", "Veuillez sélectionner un fichier CSV.")
            return False

        if not os.path.exists(self.csv_file.get()):
            messagebox.showerror("Erreur", "Le fichier CSV n'existe pas.")
            return False

        if not self.class_name.get().strip():
            messagebox.showwarning("Attention", "Veuillez entrer un nom de classe.")
            return False

        return True

    def _update_preview(self, image_path):
        """Met à jour l'aperçu dans l'interface."""
        try:
            # Charger l'image
            image = Image.open(image_path)
            self.original_image = image.copy()

            # Appliquer le zoom
            self.update_preview_zoom()

            # Supprimer le fichier temporaire
            if os.path.exists(image_path):
                os.remove(image_path)

            self.update_status("Aperçu généré avec succès")

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'affichage:\n{str(e)}")
            self.update_status("Erreur lors de l'affichage")

    def update_preview_zoom(self):
        """Met à jour l'affichage avec le zoom actuel."""
        if not hasattr(self, 'original_image') or not self.original_image:
            return

        # Calculer la nouvelle taille
        new_width = int(self.original_image.width * self.zoom_factor)
        new_height = int(self.original_image.height * self.zoom_factor)

        # Redimensionner l'image
        zoomed_image = self.original_image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        self.preview_image = ImageTk.PhotoImage(zoomed_image)

        # Effacer et afficher
        self.preview_canvas.delete("all")
        self.preview_canvas.create_image(
            new_width // 2, new_height // 2,
            image=self.preview_image,
            anchor=tk.CENTER
        )

        # Configurer la zone de défilement
        self.preview_canvas.configure(scrollregion=(0, 0, new_width, new_height))

        # Mettre à jour le label de zoom
        zoom_percent = int(self.zoom_factor * 100)
        self.zoom_label.config(text=f"{zoom_percent}%")

    def zoom_in(self):
        """Zoom avant."""
        self.zoom_factor = min(3.0, self.zoom_factor * 1.2)
        self.update_preview_zoom()

    def zoom_out(self):
        """Zoom arrière."""
        self.zoom_factor = max(0.1, self.zoom_factor / 1.2)
        self.update_preview_zoom()

    def zoom_fit(self):
        """Ajuste le zoom pour que l'image soit entièrement visible."""
        if not hasattr(self, 'original_image') or not self.original_image:
            return

        canvas_width = self.preview_canvas.winfo_width()
        canvas_height = self.preview_canvas.winfo_height()

        if canvas_width > 1 and canvas_height > 1:
            zoom_x = canvas_width / self.original_image.width
            zoom_y = canvas_height / self.original_image.height
            self.zoom_factor = min(zoom_x, zoom_y) * 0.9  # 90% pour laisser une marge
            self.update_preview_zoom()

    def on_canvas_click(self, event):
        """Gère les clics sur le canvas."""
        # Ici on pourrait ajouter des fonctionnalités d'édition directe
        pass

    def on_mouse_wheel(self, event):
        """Gère la molette de la souris pour le zoom."""
        if event.state & 0x4:  # Ctrl + molette = zoom
            if event.delta > 0:
                self.zoom_in()
            else:
                self.zoom_out()
        else:
            # Défilement normal
            self.preview_canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")

    def refresh_preview(self):
        """Actualise l'aperçu."""
        if hasattr(self, 'original_image') and self.original_image:
            self.generate_preview()

    def export_poster(self):
        """Exporte l'affiche finale."""
        if not self.current_poster:
            messagebox.showwarning("Attention", "Veuillez d'abord générer un aperçu.")
            return

        filename = filedialog.asksaveasfilename(
            title="Exporter l'affiche",
            defaultextension=".png",
            filetypes=[("PNG files", "*.png"), ("JPEG files", "*.jpg"), ("All files", "*.*")]
        )

        if filename:
            try:
                self.current_poster.save(filename)
                messagebox.showinfo("Succès", f"Affiche exportée avec succès:\n{filename}")
                self.update_status(f"Affiche exportée: {os.path.basename(filename)}")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de l'export:\n{str(e)}")

    def load_default_settings(self):
        """Charge les paramètres par défaut."""
        self.update_status("Interface prête")

    def update_status(self, message):
        """Met à jour la barre d'état."""
        self.status_label.config(text=message)
        self.root.update_idletasks()

    def run(self):
        """Lance l'application."""
        self.root.mainloop()

def main():
    """Fonction principale."""
    app = ModernPosterApp()
    app.run()

if __name__ == "__main__":
    main()
