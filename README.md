# 🎓 Générateur d'Affiches de Félicitations

Un script Python pour générer automatiquement des affiches de félicitations (tableaux d'honneur) à partir de données d'élèves au format CSV.

## ✨ Fonctionnalités

- **Lecture automatique** de fichiers CSV avec les données des élèves
- **Photos circulaires** des élèves avec gestion des images manquantes
- **Positionnement dynamique** selon le nombre d'élèves
- **Décorations** (chapeaux de graduation)
- **Couleurs personnalisées** selon les mentions
- **Templates personnalisables** pour les fonds d'affiche
- **Génération automatique** d'images PNG/JPG

## 📋 Prérequis

- Python 3.8 ou plus récent
- Pillow (traitement d'images)
- Pandas (manipulation de données)

## 🚀 Installation

1. Clonez ou téléchargez ce projet
2. Installez les dépendances :

```bash
pip install -r requirements.txt
```

## 📁 Structure du projet

```
ModelNAJA7/
├── congratulations_poster.py  # Script principal
├── requirements.txt          # Dépendances
├── README.md                # Documentation
├── data/                    # Fichiers CSV des élèves
│   └── classe_exemple.csv
├── photos/                  # Photos des élèves
├── templates/               # Images de fond
│   └── fond_defaut.png
└── output/                  # Affiches générées
    └── affiche_6ème_A.png
```

## 📊 Format du fichier CSV

Le fichier CSV doit contenir les colonnes suivantes :

| Colonne    | Description                    | Exemple           |
|------------|--------------------------------|-------------------|
| Nom        | Nom complet de l'élève        | "Alice Martin"    |
| Note       | Note sur 20                   | 18.5              |
| Classement | Position dans le classement   | 1                 |
| Mention    | Mention obtenue               | "Très Bien"       |
| Photo      | Chemin vers la photo          | "photos/alice.jpg"|

### Exemple de fichier CSV :

```csv
Nom,Note,Classement,Mention,Photo
Alice Martin,18.5,1,Très Bien,photos/alice.jpg
Bob Dupont,17.2,2,Très Bien,photos/bob.jpg
Claire Rousseau,16.8,3,Bien,photos/claire.jpg
```

## 🎯 Utilisation

### Utilisation simple

```bash
python congratulations_poster.py
```

Cette commande génère une affiche d'exemple avec des données de test.

### Utilisation avancée

```python
from congratulations_poster import CongratulationsPoster

# Initialiser le générateur
generator = CongratulationsPoster(
    template_path='templates/mon_fond.png',
    output_dir='mes_affiches'
)

# Générer une affiche
output_path = generator.generate_poster(
    csv_path='data/ma_classe.csv',
    class_name='5ème B'
)

print(f"Affiche générée: {output_path}")
```

## ⚙️ Personnalisation

### Modifier les couleurs

```python
generator.colors = {
    'text_primary': '#1A1A1A',
    'text_secondary': '#666666',
    'gold': '#FFD700',
    'silver': '#C0C0C0',
    'bronze': '#CD7F32'
}
```

### Ajuster la mise en page

```python
generator.poster_width = 1920
generator.poster_height = 1080
generator.photo_size = 150
generator.students_per_row = 5
```

## 🎨 Mentions supportées

- **Très Bien** : Couleur or
- **Bien** : Couleur argent
- **Assez Bien** : Couleur bronze
- **Passable** : Couleur grise

## 📸 Gestion des photos

- Les photos manquantes sont remplacées par un placeholder
- Format supporté : JPG, PNG, BMP, GIF
- Redimensionnement automatique en cercles
- Chemin relatif depuis le dossier du script

## 🔧 Dépannage

### Erreur "Photo non trouvée"
- Vérifiez que le chemin dans le CSV est correct
- Assurez-vous que les photos existent dans le dossier spécifié

### Problème de police
- Le script utilise Arial par défaut
- Si non disponible, utilise la police système par défaut

### Erreur de mémoire
- Réduisez la taille de l'affiche ou des photos
- Traitez moins d'élèves par affiche

## 📝 Licence

Ce projet est libre d'utilisation pour des fins éducatives et personnelles.

## 🤝 Contribution

Les améliorations et suggestions sont les bienvenues !

## 📞 Support

Pour toute question ou problème, consultez la documentation ou créez une issue.
