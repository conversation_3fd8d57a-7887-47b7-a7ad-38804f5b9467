#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Générateur d'affiches de félicitations dynamiques
=================================================

Ce script génère automatiquement des affiches de félicitations pour les élèves
à partir d'un fichier CSV contenant leurs informations.

Auteur: Assistant IA
Date: 2024
"""

import os
import pandas as pd
from PIL import Image, ImageDraw, ImageFont
import math
from typing import List, Tuple, Dict, Optional


class CongratulationsPoster:
    """
    Classe principale pour générer des affiches de félicitations.
    """

    def __init__(self, template_path: str, output_dir: str = "output"):
        """
        Initialise le générateur d'affiches.

        Args:
            template_path (str): Chemin vers l'image de fond
            output_dir (str): Dossier de sortie pour les affiches
        """
        self.template_path = template_path
        self.output_dir = output_dir
        self.create_output_directory()

        # Configuration par défaut
        self.poster_width = 1920
        self.poster_height = 1080
        self.photo_size = 120
        self.margin = 50
        self.students_per_row = 4

        # Couleurs
        self.colors = {
            'text_primary': '#2C3E50',
            'text_secondary': '#7F8C8D',
            'gold': '#F1C40F',
            'silver': '#BDC3C7',
            'bronze': '#CD7F32'
        }

    def create_output_directory(self):
        """Crée le dossier de sortie s'il n'existe pas."""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)

    def load_students_data(self, csv_path: str) -> pd.DataFrame:
        """
        Charge les données des élèves depuis un fichier CSV.

        Args:
            csv_path (str): Chemin vers le fichier CSV

        Returns:
            pd.DataFrame: Données des élèves
        """
        try:
            df = pd.read_csv(csv_path, encoding='utf-8')
            required_columns = ['Nom', 'Note', 'Classement', 'Mention', 'Photo']

            for col in required_columns:
                if col not in df.columns:
                    raise ValueError(f"Colonne manquante: {col}")

            # Trier par classement
            df = df.sort_values('Classement')
            return df

        except Exception as e:
            print(f"Erreur lors du chargement des données: {e}")
            return pd.DataFrame()

    def create_circular_photo(self, photo_path: str, size: int) -> Optional[Image.Image]:
        """
        Crée une photo circulaire à partir d'une image.

        Args:
            photo_path (str): Chemin vers la photo
            size (int): Taille du cercle

        Returns:
            Image.Image: Photo circulaire ou None si erreur
        """
        try:
            if not os.path.exists(photo_path):
                print(f"Photo non trouvée: {photo_path}")
                return self.create_default_photo(size)

            # Charger et redimensionner l'image
            img = Image.open(photo_path).convert('RGBA')
            img = img.resize((size, size), Image.Resampling.LANCZOS)

            # Créer un masque circulaire
            mask = Image.new('L', (size, size), 0)
            draw = ImageDraw.Draw(mask)
            draw.ellipse((0, 0, size, size), fill=255)

            # Appliquer le masque
            output = Image.new('RGBA', (size, size), (0, 0, 0, 0))
            output.paste(img, (0, 0))
            output.putalpha(mask)

            return output

        except Exception as e:
            print(f"Erreur lors du traitement de la photo {photo_path}: {e}")
            return self.create_default_photo(size)

    def create_default_photo(self, size: int) -> Image.Image:
        """
        Crée une photo par défaut (cercle avec initiales).

        Args:
            size (int): Taille du cercle

        Returns:
            Image.Image: Photo par défaut
        """
        img = Image.new('RGBA', (size, size), (200, 200, 200, 255))
        draw = ImageDraw.Draw(img)

        # Dessiner un cercle
        draw.ellipse((0, 0, size, size), fill=(150, 150, 150, 255))

        # Ajouter un symbole par défaut
        try:
            font = ImageFont.truetype("arial.ttf", size // 3)
        except:
            font = ImageFont.load_default()

        draw.text((size//2, size//2), "?", fill='white', font=font, anchor='mm')

        return img

    def get_mention_color(self, mention: str) -> str:
        """
        Retourne la couleur associée à une mention.

        Args:
            mention (str): Mention de l'élève

        Returns:
            str: Code couleur hexadécimal
        """
        mention_colors = {
            'Très Bien': self.colors['gold'],
            'Bien': self.colors['silver'],
            'Assez Bien': self.colors['bronze'],
            'Passable': self.colors['text_secondary']
        }
        return mention_colors.get(mention, self.colors['text_primary'])

    def calculate_dynamic_height(self, num_students: int) -> int:
        """
        Calcule la hauteur dynamique de l'affiche selon le nombre d'élèves.

        Args:
            num_students (int): Nombre d'élèves

        Returns:
            int: Hauteur calculée de l'affiche
        """
        # Hauteur de base (titre + marges)
        base_height = 300  # 250px pour titre/podium + 50px marge

        # Hauteur pour le podium (3 premiers élèves)
        podium_height = 330 if num_students >= 3 else 0  # Espace plus généreux pour le podium

        # Hauteur pour les autres élèves
        if num_students > 3:
            remaining_students = num_students - 3
            students_per_row = 4
            rows_needed = math.ceil(remaining_students / students_per_row)
            # Chaque ligne nécessite environ 220px (photo + texte + espacement plus généreux)
            other_students_height = rows_needed * 220
        elif num_students <= 3:
            # Si 3 élèves ou moins, pas de lignes supplémentaires
            other_students_height = 0 if num_students >= 3 else num_students * 220
        else:
            other_students_height = 0

        # Marge finale
        bottom_margin = 50

        total_height = base_height + podium_height + other_students_height + bottom_margin

        # Hauteur minimale et maximale
        min_height = 600
        max_height = 4000

        return max(min_height, min(max_height, total_height))

    def calculate_positions(self, num_students: int) -> List[Tuple[int, int]]:
        """
        Calcule les positions des élèves sur l'affiche avec disposition spéciale pour le podium.

        Args:
            num_students (int): Nombre d'élèves

        Returns:
            List[Tuple[int, int]]: Liste des positions (x, y)
        """
        # Ajuster la hauteur de l'affiche selon le nombre d'élèves
        self.poster_height = self.calculate_dynamic_height(num_students)

        positions = []

        # Position de départ après le titre
        start_y = 250 + self.margin

        if num_students >= 3:
            # Disposition spéciale pour les 3 premiers (podium)
            podium_positions = self.calculate_podium_positions()
            positions.extend(podium_positions)

            # Position de départ pour les autres élèves (après le podium)
            remaining_students = num_students - 3
            if remaining_students > 0:
                other_start_y = start_y + 280  # Espace plus généreux pour le podium
                other_positions = self.calculate_regular_positions(remaining_students, other_start_y)
                positions.extend(other_positions)
        else:
            # Disposition normale si moins de 3 élèves
            positions = self.calculate_regular_positions(num_students, start_y)

        return positions

    def calculate_podium_positions(self) -> List[Tuple[int, int]]:
        """
        Calcule les positions spéciales pour le podium des 3 premiers.
        Disposition: 2ème - 1er (plus haut) - 3ème

        Returns:
            List[Tuple[int, int]]: Positions pour les 3 premiers élèves
        """
        center_x = self.poster_width // 2
        podium_y = 250 + self.margin + 50  # Position Y du podium

        # Espacement horizontal entre les positions
        spacing = 300

        # Positions avec hauteurs différentes pour effet podium
        positions = [
            # 1er place au centre, plus haut
            (center_x, podium_y - 30),
            # 2ème place à gauche
            (center_x - spacing, podium_y),
            # 3ème place à droite
            (center_x + spacing, podium_y + 20)
        ]

        return positions

    def calculate_regular_positions(self, num_students: int, start_y: int) -> List[Tuple[int, int]]:
        """
        Calcule les positions normales pour les élèves après le podium.
        Organisation: 5 élèves par ligne, centrés dans la page.

        Args:
            num_students (int): Nombre d'élèves à positionner
            start_y (int): Position Y de départ

        Returns:
            List[Tuple[int, int]]: Positions des élèves
        """
        positions = []

        if num_students == 0:
            return positions

        # Configuration pour les élèves réguliers
        students_per_row = 4  # 4 élèves par ligne

        # Calculer le nombre de lignes nécessaires
        rows = math.ceil(num_students / students_per_row)

        # Zone disponible
        available_width = self.poster_width - 2 * self.margin
        remaining_height = self.poster_height - start_y - self.margin

        # Espacement entre les élèves
        spacing_x = available_width // students_per_row
        spacing_y = max(220, remaining_height // rows) if rows > 1 else 220  # Espacement généreux minimum

        for i in range(num_students):
            row = i // students_per_row
            col = i % students_per_row

            # Centrer les élèves dans chaque ligne
            students_in_row = min(students_per_row, num_students - row * students_per_row)
            row_width = students_in_row * spacing_x
            start_x = (self.poster_width - row_width) // 2 + spacing_x // 2

            x = start_x + col * spacing_x
            y = start_y + row * spacing_y

            positions.append((x, y))

        return positions

    def create_graduation_cap(self, size: int = 60) -> Image.Image:
        """
        Crée un chapeau de diplômé.

        Args:
            size (int): Taille du chapeau

        Returns:
            Image.Image: Image du chapeau de diplômé
        """
        cap = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(cap)

        # Couleur du chapeau (noir)
        cap_color = (0, 0, 0, 255)
        tassel_color = (255, 215, 0, 255)  # Or

        # Base du chapeau (carré)
        square_size = size * 0.8
        square_x = (size - square_size) // 2
        square_y = size * 0.2

        draw.rectangle([square_x, square_y, square_x + square_size, square_y + 8],
                      fill=cap_color)

        # Partie ronde du chapeau
        circle_size = size * 0.4
        circle_x = (size - circle_size) // 2
        circle_y = square_y + 5

        draw.ellipse([circle_x, circle_y, circle_x + circle_size, circle_y + circle_size * 0.6],
                    fill=cap_color)

        # Pompon (tassel)
        tassel_x = size * 0.7
        tassel_y = square_y - 5
        draw.ellipse([tassel_x, tassel_y, tassel_x + 8, tassel_y + 8], fill=tassel_color)

        # Fil du pompon
        draw.line([(tassel_x + 4, tassel_y), (square_x + square_size * 0.7, square_y)],
                 fill=tassel_color, width=2)

        return cap

    def create_crown(self, size: int = 60) -> Image.Image:
        """
        Crée une couronne pour le 1er.

        Args:
            size (int): Taille de la couronne

        Returns:
            Image.Image: Image de la couronne
        """
        crown = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(crown)

        # Couleur or
        gold_color = (255, 215, 0, 255)
        gem_color = (255, 0, 0, 255)  # Rouge pour les gemmes

        # Base de la couronne
        base_height = size * 0.3
        base_y = size * 0.6
        draw.rectangle([size * 0.1, base_y, size * 0.9, base_y + base_height],
                      fill=gold_color)

        # Pointes de la couronne
        points = [
            (size * 0.1, base_y),
            (size * 0.25, size * 0.2),
            (size * 0.4, base_y),
            (size * 0.5, size * 0.1),  # Pointe centrale plus haute
            (size * 0.6, base_y),
            (size * 0.75, size * 0.2),
            (size * 0.9, base_y)
        ]
        draw.polygon(points, fill=gold_color)

        # Gemmes
        gem_size = 6
        draw.ellipse([size * 0.5 - gem_size//2, size * 0.15,
                     size * 0.5 + gem_size//2, size * 0.15 + gem_size], fill=gem_color)

        return crown

    def create_medal(self, rank: int, size: int = 50) -> Image.Image:
        """
        Crée une médaille selon le rang.

        Args:
            rank (int): Rang de l'élève (1, 2, 3)
            size (int): Taille de la médaille

        Returns:
            Image.Image: Image de la médaille
        """
        medal = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(medal)

        # Couleurs selon le rang
        colors = {
            1: (255, 215, 0, 255),    # Or
            2: (192, 192, 192, 255),  # Argent
            3: (205, 127, 50, 255)    # Bronze
        }

        medal_color = colors.get(rank, (128, 128, 128, 255))

        # Médaille circulaire
        medal_size = size * 0.8
        medal_x = (size - medal_size) // 2
        medal_y = size * 0.2

        # Ombre
        draw.ellipse([medal_x + 2, medal_y + 2,
                     medal_x + medal_size + 2, medal_y + medal_size + 2],
                    fill=(0, 0, 0, 100))

        # Médaille
        draw.ellipse([medal_x, medal_y, medal_x + medal_size, medal_y + medal_size],
                    fill=medal_color)

        # Bordure
        draw.ellipse([medal_x, medal_y, medal_x + medal_size, medal_y + medal_size],
                    outline=(0, 0, 0, 255), width=2)

        # Numéro au centre
        try:
            font = ImageFont.truetype("arial.ttf", int(size * 0.3))
        except:
            font = ImageFont.load_default()

        text = str(rank)
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]

        text_x = medal_x + (medal_size - text_width) // 2
        text_y = medal_y + (medal_size - text_height) // 2

        draw.text((text_x, text_y), text, fill=(255, 255, 255, 255), font=font)

        # Ruban
        ribbon_width = size * 0.2
        ribbon_x = (size - ribbon_width) // 2
        draw.rectangle([ribbon_x, 0, ribbon_x + ribbon_width, medal_y + 5],
                      fill=(255, 0, 0, 255))

        return medal

    def add_podium_decorations(self, poster: Image.Image, x: int, y: int, rank: int):
        """
        Ajoute des décorations spéciales pour les 3 premiers élèves.

        Args:
            poster (Image.Image): Image de l'affiche
            x (int): Position x de l'élève
            y (int): Position y de l'élève
            rank (int): Rang de l'élève (1, 2, 3)
        """
        if rank == 1:
            # Couronne pour le 1er
            crown = self.create_crown(70)
            crown_x = x - 35
            crown_y = y - self.photo_size // 2 - 80
            poster.paste(crown, (crown_x, crown_y), crown)

            # Médaille d'or
            medal = self.create_medal(1, 60)
            medal_x = x + self.photo_size // 2 + 10
            medal_y = y - 30
            poster.paste(medal, (medal_x, medal_y), medal)

        elif rank == 2:
            # Chapeau de diplômé spécial pour le 2ème
            cap = self.create_graduation_cap(60)
            cap_x = x - 30
            cap_y = y - self.photo_size // 2 - 70
            poster.paste(cap, (cap_x, cap_y), cap)

            # Médaille d'argent
            medal = self.create_medal(2, 55)
            medal_x = x + self.photo_size // 2 + 10
            medal_y = y - 25
            poster.paste(medal, (medal_x, medal_y), medal)

        elif rank == 3:
            # Chapeau de diplômé pour le 3ème
            cap = self.create_graduation_cap(55)
            cap_x = x - 27
            cap_y = y - self.photo_size // 2 - 65
            poster.paste(cap, (cap_x, cap_y), cap)

            # Médaille de bronze
            medal = self.create_medal(3, 50)
            medal_x = x + self.photo_size // 2 + 10
            medal_y = y - 20
            poster.paste(medal, (medal_x, medal_y), medal)

    def add_decoration(self, draw: ImageDraw.Draw, x: int, y: int, decoration_type: str = "graduation_cap"):
        """
        Ajoute une décoration près de la photo de l'élève.

        Args:
            draw (ImageDraw.Draw): Objet de dessin
            x (int): Position x
            y (int): Position y
            decoration_type (str): Type de décoration
        """
        if decoration_type == "graduation_cap":
            # Dessiner un chapeau de graduation simple
            cap_size = 20
            cap_x = x + self.photo_size - cap_size
            cap_y = y - cap_size // 2

            # Base du chapeau
            draw.rectangle([cap_x, cap_y, cap_x + cap_size, cap_y + cap_size//2],
                         fill=self.colors['text_primary'])

            # Plateau du chapeau
            draw.rectangle([cap_x - 5, cap_y, cap_x + cap_size + 5, cap_y + 5],
                         fill=self.colors['text_primary'])

    def create_crown(self, size: int = 60) -> Image.Image:
        """
        Crée une couronne pour le 1er.

        Args:
            size (int): Taille de la couronne

        Returns:
            Image.Image: Image de la couronne
        """
        crown = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(crown)

        # Couleur or
        gold_color = (255, 215, 0, 255)
        gem_color = (255, 0, 0, 255)  # Rouge pour les gemmes

        # Base de la couronne
        base_height = size * 0.3
        base_y = size * 0.6
        draw.rectangle([size * 0.1, base_y, size * 0.9, base_y + base_height],
                      fill=gold_color)

        # Pointes de la couronne
        points = [
            (size * 0.1, base_y),
            (size * 0.25, size * 0.2),
            (size * 0.4, base_y),
            (size * 0.5, size * 0.1),  # Pointe centrale plus haute
            (size * 0.6, base_y),
            (size * 0.75, size * 0.2),
            (size * 0.9, base_y)
        ]
        draw.polygon(points, fill=gold_color)

        # Gemmes
        gem_size = 6
        draw.ellipse([size * 0.5 - gem_size//2, size * 0.15,
                     size * 0.5 + gem_size//2, size * 0.15 + gem_size], fill=gem_color)

        return crown

    def create_medal(self, rank: int, size: int = 50) -> Image.Image:
        """
        Crée une médaille selon le rang.

        Args:
            rank (int): Rang de l'élève (1, 2, 3)
            size (int): Taille de la médaille

        Returns:
            Image.Image: Image de la médaille
        """
        medal = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(medal)

        # Couleurs selon le rang
        colors = {
            1: (255, 215, 0, 255),    # Or
            2: (192, 192, 192, 255),  # Argent
            3: (205, 127, 50, 255)    # Bronze
        }

        medal_color = colors.get(rank, (128, 128, 128, 255))

        # Médaille circulaire
        medal_size = size * 0.8
        medal_x = (size - medal_size) // 2
        medal_y = size * 0.2

        # Ombre
        draw.ellipse([medal_x + 2, medal_y + 2,
                     medal_x + medal_size + 2, medal_y + medal_size + 2],
                    fill=(0, 0, 0, 100))

        # Médaille
        draw.ellipse([medal_x, medal_y, medal_x + medal_size, medal_y + medal_size],
                    fill=medal_color)

        # Bordure
        draw.ellipse([medal_x, medal_y, medal_x + medal_size, medal_y + medal_size],
                    outline=(0, 0, 0, 255), width=2)

        # Numéro au centre
        try:
            font = ImageFont.truetype("arial.ttf", int(size * 0.3))
        except:
            font = ImageFont.load_default()

        text = str(rank)
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]

        text_x = medal_x + (medal_size - text_width) // 2
        text_y = medal_y + (medal_size - text_height) // 2

        draw.text((text_x, text_y), text, fill=(255, 255, 255, 255), font=font)

        # Ruban
        ribbon_width = size * 0.2
        ribbon_x = (size - ribbon_width) // 2
        draw.rectangle([ribbon_x, 0, ribbon_x + ribbon_width, medal_y + 5],
                      fill=(255, 0, 0, 255))

        return medal

    def add_decoration(self, draw: ImageDraw.Draw, x: int, y: int, decoration_type: str = "graduation_cap"):
        """
        Ajoute une décoration près de la photo de l'élève.

        Args:
            draw (ImageDraw.Draw): Objet de dessin
            x (int): Position x
            y (int): Position y
            decoration_type (str): Type de décoration
        """
        if decoration_type == "graduation_cap":
            # Dessiner un chapeau de graduation simple
            cap_size = 20
            cap_x = x + self.photo_size - cap_size
            cap_y = y - cap_size // 2

            # Base du chapeau
            draw.rectangle([cap_x, cap_y, cap_x + cap_size, cap_y + cap_size//2],
                         fill=self.colors['text_primary'])

            # Plateau du chapeau
            draw.rectangle([cap_x - 5, cap_y, cap_x + cap_size + 5, cap_y + 5],
                         fill=self.colors['text_primary'])

    def generate_poster(self, csv_path: str, class_name: str = "Classe") -> str:
        """
        Génère l'affiche de félicitations complète.

        Args:
            csv_path (str): Chemin vers le fichier CSV des élèves
            class_name (str): Nom de la classe

        Returns:
            str: Chemin vers l'affiche générée
        """
        # Charger les données
        students_df = self.load_students_data(csv_path)
        if students_df.empty:
            print("Aucune donnée d'élève trouvée.")
            return ""

        # Créer l'image de base
        if os.path.exists(self.template_path):
            poster = Image.open(self.template_path).convert('RGBA')
            poster = poster.resize((self.poster_width, self.poster_height), Image.Resampling.LANCZOS)
        else:
            # Créer un fond par défaut
            poster = Image.new('RGBA', (self.poster_width, self.poster_height), (240, 248, 255, 255))

        draw = ImageDraw.Draw(poster)

        # Ajouter le titre
        self.add_title(draw, class_name)

        # Calculer les positions des élèves
        positions = self.calculate_positions(len(students_df))

        # Ajouter chaque élève
        for idx, (_, student) in enumerate(students_df.iterrows()):
            if idx < len(positions):
                x, y = positions[idx]
                rank = student['Classement']
                self.add_student_to_poster(poster, draw, student, x, y, rank)

        # Sauvegarder l'affiche
        output_path = os.path.join(self.output_dir, f"affiche_{class_name.replace(' ', '_')}.png")
        poster.save(output_path, 'PNG')

        print(f"Affiche générée: {output_path}")
        return output_path

    def add_title(self, draw: ImageDraw.Draw, class_name: str):
        """
        Ajoute le titre de l'affiche.

        Args:
            draw (ImageDraw.Draw): Objet de dessin
            class_name (str): Nom de la classe
        """
        title = f"🎓 TABLEAU D'HONNEUR - {class_name.upper()} 🎓"

        try:
            title_font = ImageFont.truetype("arial.ttf", 48)
        except:
            title_font = ImageFont.load_default()

        # Centrer le titre
        bbox = draw.textbbox((0, 0), title, font=title_font)
        title_width = bbox[2] - bbox[0]
        title_x = (self.poster_width - title_width) // 2
        title_y = 50

        # Ombre du titre
        draw.text((title_x + 2, title_y + 2), title, fill='gray', font=title_font)
        # Titre principal
        draw.text((title_x, title_y), title, fill=self.colors['text_primary'], font=title_font)

    def add_student_to_poster(self, poster: Image.Image, draw: ImageDraw.Draw,
                            student: pd.Series, x: int, y: int, rank: int = None):
        """
        Ajoute un élève à l'affiche avec décorations spéciales pour le podium.

        Args:
            poster (Image.Image): Image de l'affiche
            draw (ImageDraw.Draw): Objet de dessin
            student (pd.Series): Données de l'élève
            x (int): Position x
            y (int): Position y
            rank (int): Rang de l'élève pour les décorations spéciales
        """
        # Photo circulaire
        photo = self.create_circular_photo(student['Photo'], self.photo_size)
        if photo:
            # Centrer la photo
            photo_x = x - self.photo_size // 2
            photo_y = y - self.photo_size // 2
            poster.paste(photo, (photo_x, photo_y), photo)

        # Ajouter les décorations spéciales pour les 3 premiers
        if rank and rank <= 3:
            self.add_podium_decorations(poster, x, y, rank)
        else:
            # Décoration standard pour les autres élèves
            self.add_decoration(draw, photo_x, photo_y)

        # Informations de l'élève
        try:
            name_font = ImageFont.truetype("arial.ttf", 20)
            info_font = ImageFont.truetype("arial.ttf", 16)
        except:
            name_font = ImageFont.load_default()
            info_font = ImageFont.load_default()

        # Position du texte (sous la photo)
        text_y = y + self.photo_size // 2 + 10

        # Nom de l'élève
        name = str(student['Nom'])
        name_bbox = draw.textbbox((0, 0), name, font=name_font)
        name_width = name_bbox[2] - name_bbox[0]
        name_x = x - name_width // 2
        draw.text((name_x, text_y), name, fill=self.colors['text_primary'], font=name_font)

        # Note et classement
        note_text = f"Note: {student['Note']}/20"
        rank_text = f"#{student['Classement']}"
        mention_text = str(student['Mention'])

        # Couleur selon la mention
        mention_color = self.get_mention_color(mention_text)

        # Afficher les informations
        info_y = text_y + 25

        # Note
        note_bbox = draw.textbbox((0, 0), note_text, font=info_font)
        note_width = note_bbox[2] - note_bbox[0]
        note_x = x - note_width // 2
        draw.text((note_x, info_y), note_text, fill=self.colors['text_secondary'], font=info_font)

        # Classement
        rank_bbox = draw.textbbox((0, 0), rank_text, font=info_font)
        rank_width = rank_bbox[2] - rank_bbox[0]
        rank_x = x - rank_width // 2
        draw.text((rank_x, info_y + 20), rank_text, fill=mention_color, font=info_font)

        # Mention
        mention_bbox = draw.textbbox((0, 0), mention_text, font=info_font)
        mention_width = mention_bbox[2] - mention_bbox[0]
        mention_x = x - mention_width // 2
        draw.text((mention_x, info_y + 40), mention_text, fill=mention_color, font=info_font)


def create_sample_data():
    """
    Crée des données d'exemple pour tester le script.
    """
    # Créer les dossiers nécessaires
    directories = ['data', 'photos', 'templates', 'output']
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)

    # Données d'exemple
    sample_data = {
        'Nom': [
            'Alice Martin', 'Bob Dupont', 'Claire Rousseau', 'David Moreau',
            'Emma Bernard', 'Felix Dubois', 'Grace Leroy', 'Hugo Petit'
        ],
        'Note': [18.5, 17.2, 16.8, 16.5, 15.9, 15.2, 14.8, 14.5],
        'Classement': [1, 2, 3, 4, 5, 6, 7, 8],
        'Mention': [
            'Très Bien', 'Très Bien', 'Bien', 'Bien',
            'Bien', 'Assez Bien', 'Assez Bien', 'Assez Bien'
        ],
        'Photo': [
            'photos/alice.jpg', 'photos/bob.jpg', 'photos/claire.jpg', 'photos/david.jpg',
            'photos/emma.jpg', 'photos/felix.jpg', 'photos/grace.jpg', 'photos/hugo.jpg'
        ]
    }

    # Sauvegarder le CSV d'exemple
    df = pd.DataFrame(sample_data)
    df.to_csv('data/classe_exemple.csv', index=False, encoding='utf-8')
    print("Fichier CSV d'exemple créé: data/classe_exemple.csv")

    # Créer une image de fond simple
    create_sample_template()


def create_sample_template():
    """
    Crée un template d'image de fond simple.
    """
    template = Image.new('RGB', (1920, 1080), (240, 248, 255))
    draw = ImageDraw.Draw(template)

    # Ajouter un dégradé simple (simulation)
    for y in range(1080):
        color_value = int(240 + (y / 1080) * 15)
        color = (color_value, color_value + 8, 255)
        draw.line([(0, y), (1920, y)], fill=color)

    # Ajouter des éléments décoratifs
    # Bordure
    border_width = 20
    draw.rectangle([0, 0, 1920, border_width], fill=(70, 130, 180))
    draw.rectangle([0, 1080-border_width, 1920, 1080], fill=(70, 130, 180))
    draw.rectangle([0, 0, border_width, 1080], fill=(70, 130, 180))
    draw.rectangle([1920-border_width, 0, 1920, 1080], fill=(70, 130, 180))

    # Motifs décoratifs dans les coins
    corner_size = 100
    # Coin supérieur gauche
    draw.arc([border_width, border_width, border_width + corner_size, border_width + corner_size],
             0, 90, fill=(100, 149, 237), width=5)
    # Coin supérieur droit
    draw.arc([1920 - border_width - corner_size, border_width, 1920 - border_width, border_width + corner_size],
             90, 180, fill=(100, 149, 237), width=5)

    template.save('templates/fond_defaut.png')
    print("Template d'exemple créé: templates/fond_defaut.png")


def main():
    """
    Fonction principale pour démontrer l'utilisation du générateur d'affiches.
    """
    print("=== Générateur d'Affiches de Félicitations ===\n")

    # Créer des données d'exemple si elles n'existent pas
    if not os.path.exists('data/classe_exemple.csv'):
        print("Création des données d'exemple...")
        create_sample_data()
        print()

    # Initialiser le générateur
    template_path = 'templates/fond_defaut.png'
    generator = CongratulationsPoster(template_path)

    # Générer l'affiche
    csv_path = 'data/classe_exemple.csv'
    class_name = "6ème A"

    print(f"Génération de l'affiche pour la {class_name}...")
    output_path = generator.generate_poster(csv_path, class_name)

    if output_path:
        print(f"\n✅ Affiche générée avec succès!")
        print(f"📁 Fichier de sortie: {output_path}")
        print(f"📊 Données utilisées: {csv_path}")

        # Afficher quelques statistiques
        df = pd.read_csv(csv_path)
        print(f"\n📈 Statistiques:")
        print(f"   - Nombre d'élèves: {len(df)}")
        print(f"   - Note moyenne: {df['Note'].mean():.2f}/20")
        print(f"   - Meilleure note: {df['Note'].max()}/20")
        print(f"   - Mentions 'Très Bien': {len(df[df['Mention'] == 'Très Bien'])}")
    else:
        print("❌ Erreur lors de la génération de l'affiche.")


if __name__ == "__main__":
    main()
