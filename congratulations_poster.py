#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Générateur d'affiches de félicitations dynamiques
=================================================

Ce script génère automatiquement des affiches de félicitations pour les élèves
à partir d'un fichier CSV contenant leurs informations.

Auteur: Assistant IA
Date: 2024
"""

import os
import pandas as pd
from PIL import Image, ImageDraw, ImageFont
import math
from typing import List, Tuple, Dict, Optional


class CongratulationsPoster:
    """
    Classe principale pour générer des affiches de félicitations.
    """

    def __init__(self, template_path: str, output_dir: str = "output",
                 header_image: str = None, footer_image: str = None):
        """
        Initialise le générateur d'affiches.

        Args:
            template_path (str): Chemin vers l'image de fond
            output_dir (str): Dossier de sortie pour les affiches
            header_image (str): Chemin vers l'image d'en-tête (optionnel)
            footer_image (str): Chemin vers l'image de pied de page (optionnel)
        """
        self.template_path = template_path
        self.output_dir = output_dir
        self.header_image = header_image
        self.footer_image = footer_image
        self.create_output_directory()

        # Configuration par défaut
        self.poster_width = 1920
        self.poster_height = 1080
        self.photo_size = 120
        self.margin = 50
        self.students_per_row = 4

        # Configuration en-tête et pied de page
        self.header_height = 150  # Hauteur de la zone d'en-tête
        self.footer_height = 100  # Hauteur de la zone de pied de page

        # Configuration avancée de mise en page
        self.layout_config = {
            'podium_spacing': 350,      # Espacement horizontal du podium
            'podium_height_diff': 40,   # Différence de hauteur entre positions
            'regular_spacing_x': 180,   # Espacement horizontal régulier
            'regular_spacing_y': 200,   # Espacement vertical régulier
            'title_margin': 80,         # Marge autour du titre
            'section_spacing': 60,      # Espacement entre sections
            'photo_text_spacing': 15,   # Espacement photo-texte
            'text_line_spacing': 8,     # Espacement entre lignes de texte
            'decoration_offset': 25,    # Décalage des décorations
        }

        # Couleurs
        self.colors = {
            'text_primary': '#2C3E50',
            'text_secondary': '#7F8C8D',
            'gold': '#F1C40F',
            'silver': '#BDC3C7',
            'bronze': '#CD7F32',
            'background': '#FFD700',  # Jaune comme dans l'exemple
            'cap_color': '#000000',   # Noir pour les chapeaux
            'number_color': '#FFFFFF' # Blanc pour les numéros
        }

    def create_output_directory(self):
        """Crée le dossier de sortie s'il n'existe pas."""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)

    def load_students_data(self, csv_path: str) -> pd.DataFrame:
        """
        Charge les données des élèves depuis un fichier CSV.

        Args:
            csv_path (str): Chemin vers le fichier CSV

        Returns:
            pd.DataFrame: Données des élèves
        """
        try:
            df = pd.read_csv(csv_path, encoding='utf-8')
            required_columns = ['Nom', 'Note', 'Classement', 'Mention', 'Photo']

            for col in required_columns:
                if col not in df.columns:
                    raise ValueError(f"Colonne manquante: {col}")

            # Trier par classement
            df = df.sort_values('Classement')
            return df

        except Exception as e:
            print(f"Erreur lors du chargement des données: {e}")
            return pd.DataFrame()

    def create_circular_photo(self, photo_path: str, size: int) -> Optional[Image.Image]:
        """
        Crée une photo circulaire à partir d'une image.

        Args:
            photo_path (str): Chemin vers la photo
            size (int): Taille du cercle

        Returns:
            Image.Image: Photo circulaire ou None si erreur
        """
        try:
            if not os.path.exists(photo_path):
                print(f"Photo non trouvée: {photo_path}")
                return self.create_default_photo(size)

            # Charger et redimensionner l'image
            img = Image.open(photo_path).convert('RGBA')
            img = img.resize((size, size), Image.Resampling.LANCZOS)

            # Créer un masque circulaire
            mask = Image.new('L', (size, size), 0)
            draw = ImageDraw.Draw(mask)
            draw.ellipse((0, 0, size, size), fill=255)

            # Appliquer le masque
            output = Image.new('RGBA', (size, size), (0, 0, 0, 0))
            output.paste(img, (0, 0))
            output.putalpha(mask)

            return output

        except Exception as e:
            print(f"Erreur lors du traitement de la photo {photo_path}: {e}")
            return self.create_default_photo(size)

    def create_default_photo(self, size: int) -> Image.Image:
        """
        Crée une photo par défaut (cercle avec initiales).

        Args:
            size (int): Taille du cercle

        Returns:
            Image.Image: Photo par défaut
        """
        img = Image.new('RGBA', (size, size), (200, 200, 200, 255))
        draw = ImageDraw.Draw(img)

        # Dessiner un cercle
        draw.ellipse((0, 0, size, size), fill=(150, 150, 150, 255))

        # Ajouter un symbole par défaut
        try:
            font = ImageFont.truetype("arial.ttf", size // 3)
        except:
            font = ImageFont.load_default()

        draw.text((size//2, size//2), "?", fill='white', font=font, anchor='mm')

        return img

    def get_mention_color(self, mention: str) -> str:
        """
        Retourne la couleur associée à une mention.

        Args:
            mention (str): Mention de l'élève

        Returns:
            str: Code couleur hexadécimal
        """
        mention_colors = {
            'Très Bien': self.colors['gold'],
            'Bien': self.colors['silver'],
            'Assez Bien': self.colors['bronze'],
            'Passable': self.colors['text_secondary']
        }
        return mention_colors.get(mention, self.colors['text_primary'])

    def calculate_dynamic_height(self, num_students: int) -> int:
        """
        Calcule la hauteur dynamique de l'affiche selon le nombre d'élèves.

        Args:
            num_students (int): Nombre d'élèves

        Returns:
            int: Hauteur calculée de l'affiche
        """
        # Hauteur de base (titre + marges + en-tête + pied de page)
        base_height = 300  # 250px pour titre/podium + 50px marge

        # Ajouter l'espace pour l'en-tête et le pied de page si présents
        if self.header_image:
            base_height += self.header_height
        if self.footer_image:
            base_height += self.footer_height

        # Hauteur pour le podium (3 premiers élèves)
        podium_height = 330 if num_students >= 3 else 0  # Espace plus généreux pour le podium

        # Hauteur pour les autres élèves
        if num_students > 3:
            remaining_students = num_students - 3
            students_per_row = 10  # 10 élèves par ligne pour disposition compacte
            rows_needed = math.ceil(remaining_students / students_per_row)
            # Chaque ligne nécessite environ 180px (disposition plus compacte)
            other_students_height = rows_needed * 180
        elif num_students <= 3:
            # Si 3 élèves ou moins, pas de lignes supplémentaires
            other_students_height = 0 if num_students >= 3 else num_students * 180
        else:
            other_students_height = 0

        # Marge finale
        bottom_margin = 50

        total_height = base_height + podium_height + other_students_height + bottom_margin

        # Hauteur minimale et maximale
        min_height = 600
        max_height = 4000

        return max(min_height, min(max_height, total_height))

    def calculate_positions(self, num_students: int) -> List[Tuple[int, int]]:
        """
        Calcule les positions des élèves sur l'affiche avec disposition spéciale pour le podium.

        Args:
            num_students (int): Nombre d'élèves

        Returns:
            List[Tuple[int, int]]: Liste des positions (x, y)
        """
        # Ajuster la hauteur de l'affiche selon le nombre d'élèves
        self.poster_height = self.calculate_dynamic_height(num_students)

        positions = []

        # Position de départ après le titre (en tenant compte de l'en-tête)
        header_offset = self.header_height if self.header_image else 0
        start_y = 250 + self.margin + header_offset

        if num_students >= 3:
            # Disposition spéciale pour les 3 premiers (podium)
            podium_positions = self.calculate_podium_positions()
            positions.extend(podium_positions)

            # Position de départ pour les autres élèves (après le podium)
            remaining_students = num_students - 3
            if remaining_students > 0:
                other_start_y = start_y + 280  # Espace plus généreux pour le podium
                other_positions = self.calculate_regular_positions(remaining_students, other_start_y)
                positions.extend(other_positions)
        else:
            # Disposition normale si moins de 3 élèves
            positions = self.calculate_regular_positions(num_students, start_y)

        return positions

    def calculate_podium_positions(self) -> List[Tuple[int, int]]:
        """
        Calcule les positions spéciales pour le podium des 3 premiers avec mise en page améliorée.
        Disposition: 2ème - 1er (plus haut) - 3ème

        Returns:
            List[Tuple[int, int]]: Positions pour les 3 premiers élèves
        """
        center_x = self.poster_width // 2

        # Position Y du podium avec offset pour en-tête
        header_offset = self.header_height if self.header_image else 0
        podium_base_y = 250 + self.margin + header_offset + self.layout_config['title_margin']

        # Espacement horizontal optimisé
        spacing = self.layout_config['podium_spacing']
        height_diff = self.layout_config['podium_height_diff']

        # Positions avec effet podium plus prononcé et harmonieux
        positions = [
            # 1er place au centre, position d'honneur (plus haut)
            (center_x, podium_base_y - height_diff),
            # 2ème place à gauche, légèrement plus bas
            (center_x - spacing, podium_base_y),
            # 3ème place à droite, encore plus bas pour effet escalier
            (center_x + spacing, podium_base_y + height_diff // 2)
        ]

        return positions

    def calculate_regular_positions(self, num_students: int, start_y: int) -> List[Tuple[int, int]]:
        """
        Calcule les positions normales pour les élèves après le podium avec mise en page optimisée.
        Organisation intelligente selon le nombre d'élèves.

        Args:
            num_students (int): Nombre d'élèves à positionner
            start_y (int): Position Y de départ

        Returns:
            List[Tuple[int, int]]: Positions des élèves
        """
        positions = []

        if num_students == 0:
            return positions

        # Configuration adaptative selon le nombre d'élèves
        students_per_row = self.get_optimal_students_per_row(num_students)

        # Calculer le nombre de lignes nécessaires
        rows = math.ceil(num_students / students_per_row)

        # Zone disponible avec marges optimisées
        available_width = self.poster_width - 2 * self.margin
        footer_offset = self.footer_height if self.footer_image else 0
        remaining_height = self.poster_height - start_y - self.margin - footer_offset

        # Espacement optimisé selon la configuration
        spacing_x = min(self.layout_config['regular_spacing_x'], available_width // students_per_row)
        spacing_y = max(self.layout_config['regular_spacing_y'], remaining_height // rows) if rows > 1 else self.layout_config['regular_spacing_y']

        for i in range(num_students):
            row = i // students_per_row
            col = i % students_per_row

            # Centrer parfaitement chaque ligne
            students_in_row = min(students_per_row, num_students - row * students_per_row)

            # Calcul de la largeur réelle de la ligne
            actual_row_width = (students_in_row - 1) * spacing_x
            start_x = (self.poster_width - actual_row_width) // 2

            x = start_x + col * spacing_x
            y = start_y + row * spacing_y + self.layout_config['section_spacing']

            positions.append((x, y))

        return positions

    def get_optimal_students_per_row(self, num_students: int) -> int:
        """
        Détermine le nombre optimal d'élèves par ligne selon le nombre total.

        Args:
            num_students (int): Nombre total d'élèves

        Returns:
            int: Nombre optimal d'élèves par ligne
        """
        if num_students <= 5:
            return num_students  # Une seule ligne
        elif num_students <= 12:
            return 6  # 2 lignes de 6 max
        elif num_students <= 21:
            return 7  # 3 lignes de 7 max
        elif num_students <= 32:
            return 8  # 4 lignes de 8 max
        else:
            return 10  # 10 par ligne pour les grandes classes

    def create_graduation_cap_with_number(self, rank: int, size: int = 60) -> Image.Image:
        """
        Crée un chapeau de diplômé avec le numéro de classement.

        Args:
            rank (int): Numéro de classement de l'élève
            size (int): Taille du chapeau

        Returns:
            Image.Image: Image du chapeau de diplômé avec numéro
        """
        cap = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(cap)

        # Couleur du chapeau (noir)
        cap_color = (0, 0, 0, 255)
        tassel_color = (255, 215, 0, 255)  # Or

        # Base du chapeau (carré)
        square_size = size * 0.8
        square_x = (size - square_size) // 2
        square_y = size * 0.2

        draw.rectangle([square_x, square_y, square_x + square_size, square_y + 8],
                      fill=cap_color)

        # Partie ronde du chapeau
        circle_size = size * 0.4
        circle_x = (size - circle_size) // 2
        circle_y = square_y + 5

        draw.ellipse([circle_x, circle_y, circle_x + circle_size, circle_y + circle_size * 0.6],
                    fill=cap_color)

        # Pompon (tassel)
        tassel_x = size * 0.7
        tassel_y = square_y - 5
        draw.ellipse([tassel_x, tassel_y, tassel_x + 8, tassel_y + 8], fill=tassel_color)

        # Fil du pompon
        draw.line([(tassel_x + 4, tassel_y), (square_x + square_size * 0.7, square_y)],
                 fill=tassel_color, width=2)

        # Ajouter le numéro de classement sur le chapeau
        try:
            font = ImageFont.truetype("arial.ttf", int(size * 0.25))
        except:
            font = ImageFont.load_default()

        text = str(rank)
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]

        # Position du texte au centre du carré du chapeau
        text_x = square_x + (square_size - text_width) // 2
        text_y = square_y - text_height // 2

        # Ombre du texte
        draw.text((text_x + 1, text_y + 1), text, fill=(128, 128, 128, 255), font=font)
        # Texte principal
        draw.text((text_x, text_y), text, fill=(255, 255, 255, 255), font=font)

        return cap

    def create_graduation_cap(self, size: int = 60) -> Image.Image:
        """
        Crée un chapeau de diplômé.

        Args:
            size (int): Taille du chapeau

        Returns:
            Image.Image: Image du chapeau de diplômé
        """
        cap = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(cap)

        # Couleur du chapeau (noir)
        cap_color = (0, 0, 0, 255)
        tassel_color = (255, 215, 0, 255)  # Or

        # Base du chapeau (carré)
        square_size = size * 0.8
        square_x = (size - square_size) // 2
        square_y = size * 0.2

        draw.rectangle([square_x, square_y, square_x + square_size, square_y + 8],
                      fill=cap_color)

        # Partie ronde du chapeau
        circle_size = size * 0.4
        circle_x = (size - circle_size) // 2
        circle_y = square_y + 5

        draw.ellipse([circle_x, circle_y, circle_x + circle_size, circle_y + circle_size * 0.6],
                    fill=cap_color)

        # Pompon (tassel)
        tassel_x = size * 0.7
        tassel_y = square_y - 5
        draw.ellipse([tassel_x, tassel_y, tassel_x + 8, tassel_y + 8], fill=tassel_color)

        # Fil du pompon
        draw.line([(tassel_x + 4, tassel_y), (square_x + square_size * 0.7, square_y)],
                 fill=tassel_color, width=2)

        return cap

    def create_crown(self, size: int = 60) -> Image.Image:
        """
        Crée une couronne pour le 1er.

        Args:
            size (int): Taille de la couronne

        Returns:
            Image.Image: Image de la couronne
        """
        crown = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(crown)

        # Couleur or
        gold_color = (255, 215, 0, 255)
        gem_color = (255, 0, 0, 255)  # Rouge pour les gemmes

        # Base de la couronne
        base_height = size * 0.3
        base_y = size * 0.6
        draw.rectangle([size * 0.1, base_y, size * 0.9, base_y + base_height],
                      fill=gold_color)

        # Pointes de la couronne
        points = [
            (size * 0.1, base_y),
            (size * 0.25, size * 0.2),
            (size * 0.4, base_y),
            (size * 0.5, size * 0.1),  # Pointe centrale plus haute
            (size * 0.6, base_y),
            (size * 0.75, size * 0.2),
            (size * 0.9, base_y)
        ]
        draw.polygon(points, fill=gold_color)

        # Gemmes
        gem_size = 6
        draw.ellipse([size * 0.5 - gem_size//2, size * 0.15,
                     size * 0.5 + gem_size//2, size * 0.15 + gem_size], fill=gem_color)

        return crown

    def create_medal(self, rank: int, size: int = 50) -> Image.Image:
        """
        Crée une médaille selon le rang.

        Args:
            rank (int): Rang de l'élève (1, 2, 3)
            size (int): Taille de la médaille

        Returns:
            Image.Image: Image de la médaille
        """
        medal = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(medal)

        # Couleurs selon le rang
        colors = {
            1: (255, 215, 0, 255),    # Or
            2: (192, 192, 192, 255),  # Argent
            3: (205, 127, 50, 255)    # Bronze
        }

        medal_color = colors.get(rank, (128, 128, 128, 255))

        # Médaille circulaire
        medal_size = size * 0.8
        medal_x = (size - medal_size) // 2
        medal_y = size * 0.2

        # Ombre
        draw.ellipse([medal_x + 2, medal_y + 2,
                     medal_x + medal_size + 2, medal_y + medal_size + 2],
                    fill=(0, 0, 0, 100))

        # Médaille
        draw.ellipse([medal_x, medal_y, medal_x + medal_size, medal_y + medal_size],
                    fill=medal_color)

        # Bordure
        draw.ellipse([medal_x, medal_y, medal_x + medal_size, medal_y + medal_size],
                    outline=(0, 0, 0, 255), width=2)

        # Numéro au centre
        try:
            font = ImageFont.truetype("arial.ttf", int(size * 0.3))
        except:
            font = ImageFont.load_default()

        text = str(rank)
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]

        text_x = medal_x + (medal_size - text_width) // 2
        text_y = medal_y + (medal_size - text_height) // 2

        draw.text((text_x, text_y), text, fill=(255, 255, 255, 255), font=font)

        # Ruban
        ribbon_width = size * 0.2
        ribbon_x = (size - ribbon_width) // 2
        draw.rectangle([ribbon_x, 0, ribbon_x + ribbon_width, medal_y + 5],
                      fill=(255, 0, 0, 255))

        return medal

    def add_podium_decorations(self, poster: Image.Image, x: int, y: int, rank: int):
        """
        Ajoute des décorations spéciales pour les 3 premiers élèves.

        Args:
            poster (Image.Image): Image de l'affiche
            x (int): Position x de l'élève
            y (int): Position y de l'élève
            rank (int): Rang de l'élève (1, 2, 3)
        """
        if rank == 1:
            # Couronne pour le 1er
            crown = self.create_crown(70)
            crown_x = x - 35
            crown_y = y - self.photo_size // 2 - 80
            poster.paste(crown, (crown_x, crown_y), crown)

            # Médaille d'or
            medal = self.create_medal(1, 60)
            medal_x = x + self.photo_size // 2 + 10
            medal_y = y - 30
            poster.paste(medal, (medal_x, medal_y), medal)

        elif rank == 2:
            # Chapeau de diplômé spécial pour le 2ème
            cap = self.create_graduation_cap(60)
            cap_x = x - 30
            cap_y = y - self.photo_size // 2 - 70
            poster.paste(cap, (cap_x, cap_y), cap)

            # Médaille d'argent
            medal = self.create_medal(2, 55)
            medal_x = x + self.photo_size // 2 + 10
            medal_y = y - 25
            poster.paste(medal, (medal_x, medal_y), medal)

        elif rank == 3:
            # Chapeau de diplômé pour le 3ème
            cap = self.create_graduation_cap(55)
            cap_x = x - 27
            cap_y = y - self.photo_size // 2 - 65
            poster.paste(cap, (cap_x, cap_y), cap)

            # Médaille de bronze
            medal = self.create_medal(3, 50)
            medal_x = x + self.photo_size // 2 + 10
            medal_y = y - 20
            poster.paste(medal, (medal_x, medal_y), medal)

    def add_decoration(self, draw: ImageDraw.Draw, x: int, y: int, decoration_type: str = "graduation_cap"):
        """
        Ajoute une décoration près de la photo de l'élève.

        Args:
            draw (ImageDraw.Draw): Objet de dessin
            x (int): Position x
            y (int): Position y
            decoration_type (str): Type de décoration
        """
        if decoration_type == "graduation_cap":
            # Dessiner un chapeau de graduation simple
            cap_size = 20
            cap_x = x + self.photo_size - cap_size
            cap_y = y - cap_size // 2

            # Base du chapeau
            draw.rectangle([cap_x, cap_y, cap_x + cap_size, cap_y + cap_size//2],
                         fill=self.colors['text_primary'])

            # Plateau du chapeau
            draw.rectangle([cap_x - 5, cap_y, cap_x + cap_size + 5, cap_y + 5],
                         fill=self.colors['text_primary'])

    def create_crown(self, size: int = 60) -> Image.Image:
        """
        Crée une couronne pour le 1er.

        Args:
            size (int): Taille de la couronne

        Returns:
            Image.Image: Image de la couronne
        """
        crown = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(crown)

        # Couleur or
        gold_color = (255, 215, 0, 255)
        gem_color = (255, 0, 0, 255)  # Rouge pour les gemmes

        # Base de la couronne
        base_height = size * 0.3
        base_y = size * 0.6
        draw.rectangle([size * 0.1, base_y, size * 0.9, base_y + base_height],
                      fill=gold_color)

        # Pointes de la couronne
        points = [
            (size * 0.1, base_y),
            (size * 0.25, size * 0.2),
            (size * 0.4, base_y),
            (size * 0.5, size * 0.1),  # Pointe centrale plus haute
            (size * 0.6, base_y),
            (size * 0.75, size * 0.2),
            (size * 0.9, base_y)
        ]
        draw.polygon(points, fill=gold_color)

        # Gemmes
        gem_size = 6
        draw.ellipse([size * 0.5 - gem_size//2, size * 0.15,
                     size * 0.5 + gem_size//2, size * 0.15 + gem_size], fill=gem_color)

        return crown

    def create_medal(self, rank: int, size: int = 50) -> Image.Image:
        """
        Crée une médaille selon le rang.

        Args:
            rank (int): Rang de l'élève (1, 2, 3)
            size (int): Taille de la médaille

        Returns:
            Image.Image: Image de la médaille
        """
        medal = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(medal)

        # Couleurs selon le rang
        colors = {
            1: (255, 215, 0, 255),    # Or
            2: (192, 192, 192, 255),  # Argent
            3: (205, 127, 50, 255)    # Bronze
        }

        medal_color = colors.get(rank, (128, 128, 128, 255))

        # Médaille circulaire
        medal_size = size * 0.8
        medal_x = (size - medal_size) // 2
        medal_y = size * 0.2

        # Ombre
        draw.ellipse([medal_x + 2, medal_y + 2,
                     medal_x + medal_size + 2, medal_y + medal_size + 2],
                    fill=(0, 0, 0, 100))

        # Médaille
        draw.ellipse([medal_x, medal_y, medal_x + medal_size, medal_y + medal_size],
                    fill=medal_color)

        # Bordure
        draw.ellipse([medal_x, medal_y, medal_x + medal_size, medal_y + medal_size],
                    outline=(0, 0, 0, 255), width=2)

        # Numéro au centre
        try:
            font = ImageFont.truetype("arial.ttf", int(size * 0.3))
        except:
            font = ImageFont.load_default()

        text = str(rank)
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]

        text_x = medal_x + (medal_size - text_width) // 2
        text_y = medal_y + (medal_size - text_height) // 2

        draw.text((text_x, text_y), text, fill=(255, 255, 255, 255), font=font)

        # Ruban
        ribbon_width = size * 0.2
        ribbon_x = (size - ribbon_width) // 2
        draw.rectangle([ribbon_x, 0, ribbon_x + ribbon_width, medal_y + 5],
                      fill=(255, 0, 0, 255))

        return medal

    def create_colored_background(self) -> Image.Image:
        """
        Crée un fond coloré avec motifs décoratifs comme dans l'exemple.

        Returns:
            Image.Image: Image de fond colorée
        """
        # Convertir la couleur hex en RGB
        bg_color = self.hex_to_rgb(self.colors['background'])

        # Créer l'image de base
        background = Image.new('RGB', (self.poster_width, self.poster_height), bg_color)
        draw = ImageDraw.Draw(background)

        # Ajouter des motifs décoratifs (étoiles, points, etc.)
        self.add_decorative_patterns(draw)

        return background.convert('RGBA')

    def hex_to_rgb(self, hex_color: str) -> tuple:
        """
        Convertit une couleur hexadécimale en RGB.

        Args:
            hex_color (str): Couleur au format #RRGGBB

        Returns:
            tuple: Couleur au format (R, G, B)
        """
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))

    def add_decorative_patterns(self, draw: ImageDraw.Draw):
        """
        Ajoute des motifs décoratifs au fond (étoiles, points, etc.).

        Args:
            draw (ImageDraw.Draw): Objet de dessin
        """
        import random

        # Couleur des motifs (plus foncée que le fond)
        pattern_color = (200, 180, 0)  # Jaune plus foncé

        # Ajouter des étoiles dispersées
        for _ in range(50):
            x = random.randint(50, self.poster_width - 50)
            y = random.randint(50, self.poster_height - 50)
            self.draw_star(draw, x, y, 8, pattern_color)

        # Ajouter des points décoratifs
        for _ in range(100):
            x = random.randint(0, self.poster_width)
            y = random.randint(0, self.poster_height)
            draw.ellipse([x, y, x + 3, y + 3], fill=pattern_color)

    def draw_star(self, draw: ImageDraw.Draw, x: int, y: int, size: int, color: tuple):
        """
        Dessine une étoile à 5 branches.

        Args:
            draw (ImageDraw.Draw): Objet de dessin
            x (int): Position x du centre
            y (int): Position y du centre
            size (int): Taille de l'étoile
            color (tuple): Couleur de l'étoile
        """
        import math

        points = []
        for i in range(10):
            angle = math.pi * i / 5
            if i % 2 == 0:
                # Points extérieurs
                px = x + size * math.cos(angle)
                py = y + size * math.sin(angle)
            else:
                # Points intérieurs
                px = x + (size * 0.4) * math.cos(angle)
                py = y + (size * 0.4) * math.sin(angle)
            points.append((px, py))

        draw.polygon(points, fill=color)

    def add_header_image(self, poster: Image.Image):
        """
        Ajoute une image d'en-tête à l'affiche.

        Args:
            poster (Image.Image): Image de l'affiche
        """
        if not self.header_image or not os.path.exists(self.header_image):
            return

        try:
            # Charger l'image d'en-tête
            header_img = Image.open(self.header_image).convert('RGBA')

            # Calculer les dimensions pour maintenir le ratio
            header_width = self.poster_width - (2 * self.margin)
            header_ratio = header_img.height / header_img.width
            calculated_height = int(header_width * header_ratio)

            # Limiter la hauteur si nécessaire
            if calculated_height > self.header_height:
                calculated_height = self.header_height
                header_width = int(calculated_height / header_ratio)

            # Redimensionner l'image
            header_img = header_img.resize((header_width, calculated_height), Image.Resampling.LANCZOS)

            # Position centrée en haut
            x = (self.poster_width - header_width) // 2
            y = self.margin // 2

            # Coller l'image
            poster.paste(header_img, (x, y), header_img)

        except Exception as e:
            print(f"Erreur lors de l'ajout de l'image d'en-tête: {e}")

    def add_footer_image(self, poster: Image.Image):
        """
        Ajoute une image de pied de page à l'affiche.

        Args:
            poster (Image.Image): Image de l'affiche
        """
        if not self.footer_image or not os.path.exists(self.footer_image):
            return

        try:
            # Charger l'image de pied de page
            footer_img = Image.open(self.footer_image).convert('RGBA')

            # Calculer les dimensions pour maintenir le ratio
            footer_width = self.poster_width - (2 * self.margin)
            footer_ratio = footer_img.height / footer_img.width
            calculated_height = int(footer_width * footer_ratio)

            # Limiter la hauteur si nécessaire
            if calculated_height > self.footer_height:
                calculated_height = self.footer_height
                footer_width = int(calculated_height / footer_ratio)

            # Redimensionner l'image
            footer_img = footer_img.resize((footer_width, calculated_height), Image.Resampling.LANCZOS)

            # Position centrée en bas
            x = (self.poster_width - footer_width) // 2
            y = self.poster_height - calculated_height - (self.margin // 2)

            # Coller l'image
            poster.paste(footer_img, (x, y), footer_img)

        except Exception as e:
            print(f"Erreur lors de l'ajout de l'image de pied de page: {e}")

    def add_corner_logos(self, poster: Image.Image, logo_path: str = None):
        """
        Ajoute des logos dans les coins de l'affiche.

        Args:
            poster (Image.Image): Image de l'affiche
            logo_path (str): Chemin vers le logo à placer dans les coins
        """
        if not logo_path or not os.path.exists(logo_path):
            return

        try:
            # Charger le logo
            logo = Image.open(logo_path).convert('RGBA')

            # Redimensionner le logo (taille fixe pour les coins)
            logo_size = 80
            logo = logo.resize((logo_size, logo_size), Image.Resampling.LANCZOS)

            # Positions des coins
            margin = 20
            positions = [
                (margin, margin),  # Coin supérieur gauche
                (self.poster_width - logo_size - margin, margin),  # Coin supérieur droit
                (margin, self.poster_height - logo_size - margin),  # Coin inférieur gauche
                (self.poster_width - logo_size - margin, self.poster_height - logo_size - margin)  # Coin inférieur droit
            ]

            # Ajouter le logo dans chaque coin
            for x, y in positions:
                poster.paste(logo, (x, y), logo)

        except Exception as e:
            print(f"Erreur lors de l'ajout des logos de coin: {e}")

    def add_decoration(self, draw: ImageDraw.Draw, x: int, y: int, decoration_type: str = "graduation_cap"):
        """
        Ajoute une décoration près de la photo de l'élève.

        Args:
            draw (ImageDraw.Draw): Objet de dessin
            x (int): Position x
            y (int): Position y
            decoration_type (str): Type de décoration
        """
        if decoration_type == "graduation_cap":
            # Dessiner un chapeau de graduation simple
            cap_size = 20
            cap_x = x + self.photo_size - cap_size
            cap_y = y - cap_size // 2

            # Base du chapeau
            draw.rectangle([cap_x, cap_y, cap_x + cap_size, cap_y + cap_size//2],
                         fill=self.colors['text_primary'])

            # Plateau du chapeau
            draw.rectangle([cap_x - 5, cap_y, cap_x + cap_size + 5, cap_y + 5],
                         fill=self.colors['text_primary'])

    def generate_poster(self, csv_path: str, class_name: str = "Classe") -> str:
        """
        Génère l'affiche de félicitations complète.

        Args:
            csv_path (str): Chemin vers le fichier CSV des élèves
            class_name (str): Nom de la classe

        Returns:
            str: Chemin vers l'affiche générée
        """
        # Charger les données
        students_df = self.load_students_data(csv_path)
        if students_df.empty:
            print("Aucune donnée d'élève trouvée.")
            return ""

        # Créer l'image de base
        if os.path.exists(self.template_path):
            poster = Image.open(self.template_path).convert('RGBA')
            poster = poster.resize((self.poster_width, self.poster_height), Image.Resampling.LANCZOS)
        else:
            # Créer un fond coloré comme dans l'exemple
            poster = self.create_colored_background()

        draw = ImageDraw.Draw(poster)

        # Ajouter les images d'en-tête et de pied de page
        self.add_header_image(poster)
        self.add_footer_image(poster)

        # Ajouter le titre
        self.add_title(draw, class_name)

        # Calculer les positions des élèves
        positions = self.calculate_positions(len(students_df))

        # Ajouter chaque élève
        for idx, (_, student) in enumerate(students_df.iterrows()):
            if idx < len(positions):
                x, y = positions[idx]
                rank = student['Classement']
                self.add_student_to_poster(poster, draw, student, x, y, rank)

        # Sauvegarder l'affiche
        output_path = os.path.join(self.output_dir, f"affiche_{class_name.replace(' ', '_')}.png")
        poster.save(output_path, 'PNG')

        print(f"Affiche générée: {output_path}")
        return output_path

    def add_title(self, draw: ImageDraw.Draw, class_name: str):
        """
        Ajoute le titre de l'affiche avec une mise en page améliorée.

        Args:
            draw (ImageDraw.Draw): Objet de dessin
            class_name (str): Nom de la classe
        """
        # Titre principal avec style amélioré
        title_main = f"TABLEAU D'HONNEUR"
        title_class = f"{class_name.upper()}"

        try:
            title_font = ImageFont.truetype("arial.ttf", 52)  # Plus grand
            subtitle_font = ImageFont.truetype("arial.ttf", 36)
            decoration_font = ImageFont.truetype("arial.ttf", 24)
        except:
            title_font = ImageFont.load_default()
            subtitle_font = ImageFont.load_default()
            decoration_font = ImageFont.load_default()

        # Position de base avec offset pour en-tête
        header_offset = self.header_height if self.header_image else 0
        base_y = self.layout_config['title_margin'] + header_offset

        # Titre principal centré
        title_bbox = draw.textbbox((0, 0), title_main, font=title_font)
        title_width = title_bbox[2] - title_bbox[0]
        title_x = (self.poster_width - title_width) // 2
        title_y = base_y

        # Fond décoratif pour le titre
        padding = 20
        bg_x1 = title_x - padding
        bg_y1 = title_y - 10
        bg_x2 = title_x + title_width + padding
        bg_y2 = title_y + 60

        # Dégradé de fond
        draw.rectangle([bg_x1, bg_y1, bg_x2, bg_y2], fill=(255, 255, 255, 150))
        draw.rectangle([bg_x1 + 2, bg_y1 + 2, bg_x2 - 2, bg_y2 - 2],
                      outline=self.colors['gold'], width=3)

        # Ombre du titre principal
        draw.text((title_x + 3, title_y + 3), title_main, fill=(0, 0, 0, 100), font=title_font)
        # Titre principal
        draw.text((title_x, title_y), title_main, fill=self.colors['text_primary'], font=title_font)

        # Sous-titre (nom de la classe)
        subtitle_y = title_y + 65
        subtitle_bbox = draw.textbbox((0, 0), title_class, font=subtitle_font)
        subtitle_width = subtitle_bbox[2] - subtitle_bbox[0]
        subtitle_x = (self.poster_width - subtitle_width) // 2

        # Ombre du sous-titre
        draw.text((subtitle_x + 2, subtitle_y + 2), title_class, fill=(0, 0, 0, 80), font=subtitle_font)
        # Sous-titre
        draw.text((subtitle_x, subtitle_y), title_class, fill=self.colors['gold'], font=subtitle_font)

        # Décorations latérales
        decoration_y = title_y + 20
        left_decoration = "🎓 ⭐ 🏆"
        right_decoration = "🏆 ⭐ 🎓"

        # Décoration gauche
        left_x = title_x - 150
        draw.text((left_x, decoration_y), left_decoration, fill=self.colors['gold'], font=decoration_font)

        # Décoration droite
        right_x = title_x + title_width + 50
        draw.text((right_x, decoration_y), right_decoration, fill=self.colors['gold'], font=decoration_font)

        # Ligne décorative sous le titre
        line_y = subtitle_y + 50
        line_width = min(800, self.poster_width - 200)
        line_x1 = (self.poster_width - line_width) // 2
        line_x2 = line_x1 + line_width

        # Ligne principale
        draw.line([(line_x1, line_y), (line_x2, line_y)], fill=self.colors['gold'], width=4)
        # Ligne d'accent
        draw.line([(line_x1 + 50, line_y + 8), (line_x2 - 50, line_y + 8)],
                 fill=self.colors['text_secondary'], width=2)

    def add_student_to_poster(self, poster: Image.Image, draw: ImageDraw.Draw,
                            student: pd.Series, x: int, y: int, rank: int = None):
        """
        Ajoute un élève à l'affiche avec décorations spéciales pour le podium.

        Args:
            poster (Image.Image): Image de l'affiche
            draw (ImageDraw.Draw): Objet de dessin
            student (pd.Series): Données de l'élève
            x (int): Position x
            y (int): Position y
            rank (int): Rang de l'élève pour les décorations spéciales
        """
        # Photo circulaire
        photo = self.create_circular_photo(student['Photo'], self.photo_size)
        if photo:
            # Centrer la photo
            photo_x = x - self.photo_size // 2
            photo_y = y - self.photo_size // 2
            poster.paste(photo, (photo_x, photo_y), photo)

        # Ajouter les décorations spéciales pour les 3 premiers
        if rank and rank <= 3:
            self.add_podium_decorations(poster, x, y, rank)

        # Ajouter un chapeau avec numéro pour TOUS les élèves (comme dans l'exemple)
        if rank:
            cap_with_number = self.create_graduation_cap_with_number(rank, 50)
            cap_x = x - 25
            cap_y = y - self.photo_size // 2 - 60
            poster.paste(cap_with_number, (cap_x, cap_y), cap_with_number)

        # Informations de l'élève avec mise en page améliorée
        self.add_student_text_improved(draw, student, x, y)

    def add_student_text_improved(self, draw: ImageDraw.Draw, student: dict, x: int, y: int):
        """
        Ajoute les informations textuelles de l'élève avec une mise en page améliorée.

        Args:
            draw (ImageDraw.Draw): Objet de dessin
            student (dict): Données de l'élève
            x (int): Position x centrale
            y (int): Position y centrale
        """
        # Polices avec tailles optimisées
        try:
            name_font = ImageFont.truetype("arial.ttf", 22)  # Plus grand pour le nom
            info_font = ImageFont.truetype("arial.ttf", 16)
            small_font = ImageFont.truetype("arial.ttf", 14)
        except:
            name_font = ImageFont.load_default()
            info_font = ImageFont.load_default()
            small_font = ImageFont.load_default()

        # Position de départ du texte avec espacement configuré
        text_start_y = y + self.photo_size // 2 + self.layout_config['photo_text_spacing']

        # Nom de l'élève avec style amélioré
        name = str(student['Nom'])
        name_bbox = draw.textbbox((0, 0), name, font=name_font)
        name_width = name_bbox[2] - name_bbox[0]
        name_x = x - name_width // 2

        # Ombre subtile pour le nom
        draw.text((name_x + 1, text_start_y + 1), name, fill=(0, 0, 0, 80), font=name_font)
        # Nom principal
        draw.text((name_x, text_start_y), name, fill=self.colors['text_primary'], font=name_font)

        # Informations organisées en bloc compact
        current_y = text_start_y + 30

        # Note avec style amélioré
        note_text = f"{student['Note']}/20"
        note_bbox = draw.textbbox((0, 0), note_text, font=info_font)
        note_width = note_bbox[2] - note_bbox[0]
        note_x = x - note_width // 2

        # Fond subtil pour la note
        padding = 4
        draw.rectangle([note_x - padding, current_y - 2,
                       note_x + note_width + padding, current_y + 18],
                      fill=(255, 255, 255, 100))
        draw.text((note_x, current_y), note_text, fill=self.colors['text_primary'], font=info_font)

        # Classement et mention sur la même ligne
        current_y += 25

        rank_text = f"#{student['Classement']}"
        mention_text = str(student['Mention'])
        mention_color = self.get_mention_color(mention_text)

        # Calculer la largeur totale pour centrer
        rank_bbox = draw.textbbox((0, 0), rank_text, font=small_font)
        mention_bbox = draw.textbbox((0, 0), mention_text, font=small_font)

        total_width = (rank_bbox[2] - rank_bbox[0]) + 20 + (mention_bbox[2] - mention_bbox[0])
        start_x = x - total_width // 2

        # Classement
        draw.text((start_x, current_y), rank_text, fill=mention_color, font=small_font)

        # Séparateur
        sep_x = start_x + (rank_bbox[2] - rank_bbox[0]) + 10
        draw.text((sep_x, current_y), "•", fill=self.colors['text_secondary'], font=small_font)

        # Mention
        mention_x = sep_x + 10
        draw.text((mention_x, current_y), mention_text, fill=mention_color, font=small_font)


def create_sample_data():
    """
    Crée des données d'exemple pour tester le script.
    """
    # Créer les dossiers nécessaires
    directories = ['data', 'photos', 'templates', 'output']
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)

    # Données d'exemple
    sample_data = {
        'Nom': [
            'Alice Martin', 'Bob Dupont', 'Claire Rousseau', 'David Moreau',
            'Emma Bernard', 'Felix Dubois', 'Grace Leroy', 'Hugo Petit'
        ],
        'Note': [18.5, 17.2, 16.8, 16.5, 15.9, 15.2, 14.8, 14.5],
        'Classement': [1, 2, 3, 4, 5, 6, 7, 8],
        'Mention': [
            'Très Bien', 'Très Bien', 'Bien', 'Bien',
            'Bien', 'Assez Bien', 'Assez Bien', 'Assez Bien'
        ],
        'Photo': [
            'photos/alice.jpg', 'photos/bob.jpg', 'photos/claire.jpg', 'photos/david.jpg',
            'photos/emma.jpg', 'photos/felix.jpg', 'photos/grace.jpg', 'photos/hugo.jpg'
        ]
    }

    # Sauvegarder le CSV d'exemple
    df = pd.DataFrame(sample_data)
    df.to_csv('data/classe_exemple.csv', index=False, encoding='utf-8')
    print("Fichier CSV d'exemple créé: data/classe_exemple.csv")

    # Créer une image de fond simple
    create_sample_template()


def create_sample_template():
    """
    Crée un template d'image de fond simple.
    """
    template = Image.new('RGB', (1920, 1080), (240, 248, 255))
    draw = ImageDraw.Draw(template)

    # Ajouter un dégradé simple (simulation)
    for y in range(1080):
        color_value = int(240 + (y / 1080) * 15)
        color = (color_value, color_value + 8, 255)
        draw.line([(0, y), (1920, y)], fill=color)

    # Ajouter des éléments décoratifs
    # Bordure
    border_width = 20
    draw.rectangle([0, 0, 1920, border_width], fill=(70, 130, 180))
    draw.rectangle([0, 1080-border_width, 1920, 1080], fill=(70, 130, 180))
    draw.rectangle([0, 0, border_width, 1080], fill=(70, 130, 180))
    draw.rectangle([1920-border_width, 0, 1920, 1080], fill=(70, 130, 180))

    # Motifs décoratifs dans les coins
    corner_size = 100
    # Coin supérieur gauche
    draw.arc([border_width, border_width, border_width + corner_size, border_width + corner_size],
             0, 90, fill=(100, 149, 237), width=5)
    # Coin supérieur droit
    draw.arc([1920 - border_width - corner_size, border_width, 1920 - border_width, border_width + corner_size],
             90, 180, fill=(100, 149, 237), width=5)

    template.save('templates/fond_defaut.png')
    print("Template d'exemple créé: templates/fond_defaut.png")


def main():
    """
    Fonction principale pour démontrer l'utilisation du générateur d'affiches.
    """
    print("=== Générateur d'Affiches de Félicitations ===\n")

    # Créer des données d'exemple si elles n'existent pas
    if not os.path.exists('data/classe_exemple.csv'):
        print("Création des données d'exemple...")
        create_sample_data()
        print()

    # Initialiser le générateur
    template_path = 'templates/fond_defaut.png'
    generator = CongratulationsPoster(template_path)

    # Générer l'affiche
    csv_path = 'data/classe_exemple.csv'
    class_name = "6ème A"

    print(f"Génération de l'affiche pour la {class_name}...")
    output_path = generator.generate_poster(csv_path, class_name)

    if output_path:
        print(f"\n✅ Affiche générée avec succès!")
        print(f"📁 Fichier de sortie: {output_path}")
        print(f"📊 Données utilisées: {csv_path}")

        # Afficher quelques statistiques
        df = pd.read_csv(csv_path)
        print(f"\n📈 Statistiques:")
        print(f"   - Nombre d'élèves: {len(df)}")
        print(f"   - Note moyenne: {df['Note'].mean():.2f}/20")
        print(f"   - Meilleure note: {df['Note'].max()}/20")
        print(f"   - Mentions 'Très Bien': {len(df[df['Mention'] == 'Très Bien'])}")
    else:
        print("❌ Erreur lors de la génération de l'affiche.")


if __name__ == "__main__":
    main()
