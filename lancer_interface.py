#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Lanceur pour l'interface graphique du générateur d'affiches
==========================================================

Script simple pour lancer l'interface Windows avec gestion d'erreurs.
"""

import sys
import os

def check_dependencies():
    """Vérifie que toutes les dépendances sont installées."""
    missing_modules = []
    
    try:
        import tkinter
    except ImportError:
        missing_modules.append("tkinter")
    
    try:
        import pandas
    except ImportError:
        missing_modules.append("pandas")
    
    try:
        from PIL import Image, ImageTk
    except ImportError:
        missing_modules.append("Pillow")
    
    try:
        from congratulations_poster import CongratulationsPoster
    except ImportError:
        missing_modules.append("congratulations_poster (module principal)")
    
    try:
        from config import get_config
    except ImportError:
        missing_modules.append("config (module de configuration)")
    
    return missing_modules

def main():
    """Fonction principale de lancement."""
    print("🎓 Lancement de l'Interface Graphique")
    print("=" * 40)
    
    # Vérifier les dépendances
    print("🔍 Vérification des dépendances...")
    missing = check_dependencies()
    
    if missing:
        print("❌ Modules manquants:")
        for module in missing:
            print(f"   - {module}")
        print("\n💡 Pour installer les dépendances:")
        print("   pip install -r requirements.txt")
        print("   python setup.py")
        input("\nAppuyez sur Entrée pour quitter...")
        return False
    
    print("✅ Toutes les dépendances sont installées")
    
    # Vérifier les fichiers nécessaires
    print("📁 Vérification des fichiers...")
    required_files = [
        "interface_gui.py",
        "congratulations_poster.py",
        "config.py"
    ]
    
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print("❌ Fichiers manquants:")
        for file in missing_files:
            print(f"   - {file}")
        input("\nAppuyez sur Entrée pour quitter...")
        return False
    
    print("✅ Tous les fichiers sont présents")
    
    # Créer les dossiers nécessaires
    print("📂 Création des dossiers...")
    directories = ['data', 'photos', 'templates', 'output']
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"   ✅ Dossier créé: {directory}/")
        else:
            print(f"   📁 Dossier existant: {directory}/")
    
    # Lancer l'interface
    print("\n🚀 Lancement de l'interface graphique...")
    
    try:
        from interface_gui import main as gui_main
        gui_main()
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du lancement: {e}")
        
        # Afficher l'erreur détaillée
        import traceback
        print("\n📋 Détails de l'erreur:")
        print(traceback.format_exc())
        
        input("\nAppuyez sur Entrée pour quitter...")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n👋 Arrêt demandé par l'utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur inattendue: {e}")
        input("Appuyez sur Entrée pour quitter...")
        sys.exit(1)
