@echo off
title Generateur d'Affiches - Interface Graphique
color 0B

echo.
echo ========================================
echo   GENERATEUR D'AFFICHES DE FELICITATIONS
echo ========================================
echo.
echo Lancement de l'interface graphique...
echo.

REM Verifier si Python est installe
python --version >nul 2>&1
if errorlevel 1 (
    echo ERREUR: Python n'est pas installe ou pas dans le PATH
    echo.
    echo Veuillez installer Python depuis https://python.org
    echo.
    pause
    exit /b 1
)

REM Verifier si le fichier principal existe
if not exist "lancer_interface.py" (
    echo ERREUR: Fichier lancer_interface.py non trouve
    echo.
    echo Assurez-vous d'etre dans le bon dossier
    echo.
    pause
    exit /b 1
)

REM Lancer l'interface
echo Demarrage de l'interface...
python lancer_interface.py

REM Si erreur, afficher un message
if errorlevel 1 (
    echo.
    echo Une erreur s'est produite lors du lancement
    echo.
    pause
)

echo.
echo Interface fermee.
pause
