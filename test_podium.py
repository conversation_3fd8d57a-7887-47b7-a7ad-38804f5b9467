#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test du nouveau système de podium avec décorations spéciales
"""

import os
import pandas as pd
from congratulations_poster import Congratulations<PERSON>oster

def create_test_data():
    """Crée des données de test avec plus d'élèves."""
    # Données d'exemple avec 12 élèves
    sample_data = {
        'Nom': [
            '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
            '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
            '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>'
        ],
        'Note': [18.5, 17.2, 16.8, 16.5, 15.9, 15.2, 14.8, 14.5, 14.2, 13.8, 13.5, 13.0],
        'Classement': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
        'Mention': [
            '<PERSON><PERSON><PERSON> Bien', '<PERSON><PERSON><PERSON> Bien', '<PERSON><PERSON>', '<PERSON><PERSON>',
            '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
            '<PERSON><PERSON><PERSON>', 'Passable', 'Passable', 'Passable'
        ],
        'Photo': [
            'photos/alice.jpg', 'photos/bob.jpg', 'photos/claire.jpg', 'photos/david.jpg',
            'photos/emma.jpg', 'photos/felix.jpg', 'photos/grace.jpg', 'photos/hugo.jpg',
            'photos/iris.jpg', 'photos/jules.jpg', 'photos/karine.jpg', 'photos/louis.jpg'
        ]
    }

    # Créer les dossiers nécessaires
    directories = ['data', 'photos', 'templates', 'output']
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)

    # Sauvegarder le CSV de test
    df = pd.DataFrame(sample_data)
    df.to_csv('data/test_podium.csv', index=False, encoding='utf-8')
    print("✅ Fichier CSV de test créé: data/test_podium.csv")
    
    return 'data/test_podium.csv'

def test_podium_layout():
    """Test la nouvelle disposition avec podium."""
    print("🎯 Test du système de podium avec décorations spéciales")
    print("=" * 60)
    
    # Créer les données de test
    csv_path = create_test_data()
    
    # Initialiser le générateur
    template_path = 'templates/fond_defaut.png'
    generator = CongratulationsPoster(template_path)
    
    # Configuration pour le test
    generator.poster_width = 1920
    generator.poster_height = 1080
    generator.photo_size = 120
    
    print(f"\n📊 Configuration:")
    print(f"   - Dimensions: {generator.poster_width}x{generator.poster_height}")
    print(f"   - Taille des photos: {generator.photo_size}px")
    print(f"   - Disposition: Podium pour les 3 premiers + 5 par ligne pour les autres")
    
    # Générer l'affiche
    class_name = "Test Podium - 6ème A"
    print(f"\n🎨 Génération de l'affiche pour '{class_name}'...")
    
    output_path = generator.generate_poster(csv_path, class_name)
    
    if output_path and os.path.exists(output_path):
        print(f"\n✅ Affiche générée avec succès!")
        print(f"📁 Fichier: {output_path}")
        
        # Afficher les statistiques
        df = pd.read_csv(csv_path)
        print(f"\n📈 Statistiques:")
        print(f"   - Nombre total d'élèves: {len(df)}")
        print(f"   - Élèves sur le podium: 3 (avec décorations spéciales)")
        print(f"   - Autres élèves: {len(df) - 3} (5 par ligne)")
        print(f"   - Note moyenne: {df['Note'].mean():.2f}/20")
        
        # Détails du podium
        podium = df.head(3)
        print(f"\n🏆 Podium:")
        for idx, (_, student) in enumerate(podium.iterrows()):
            rank = student['Classement']
            decorations = {
                1: "👑 Couronne + 🥇 Médaille d'or",
                2: "🎓 Chapeau spécial + 🥈 Médaille d'argent", 
                3: "🎓 Chapeau + 🥉 Médaille de bronze"
            }
            print(f"   {rank}. {student['Nom']} - {student['Note']}/20 - {decorations[rank]}")
        
        # Détails des autres élèves
        others = df.tail(len(df) - 3)
        print(f"\n👥 Autres élèves (style uniforme, 5 par ligne):")
        for idx, (_, student) in enumerate(others.iterrows()):
            print(f"   {student['Classement']}. {student['Nom']} - {student['Note']}/20")
            
        file_size = os.path.getsize(output_path) / (1024 * 1024)  # MB
        print(f"\n💾 Taille du fichier: {file_size:.2f} MB")
        
        return True
    else:
        print("❌ Erreur lors de la génération de l'affiche.")
        return False

def main():
    """Fonction principale de test."""
    print("🎓 Test du Générateur d'Affiches avec Podium")
    print("=" * 50)
    
    success = test_podium_layout()
    
    if success:
        print(f"\n🎉 Test réussi!")
        print(f"💡 Fonctionnalités testées:")
        print(f"   ✅ Disposition spéciale pour les 3 premiers (podium)")
        print(f"   ✅ Décorations spéciales (couronnes, médailles, chapeaux)")
        print(f"   ✅ Organisation des autres élèves (5 par ligne)")
        print(f"   ✅ Centrage automatique")
        print(f"   ✅ Génération d'affiche complète")
    else:
        print(f"\n❌ Test échoué!")

if __name__ == "__main__":
    main()
