#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Démonstration complète du générateur d'affiches de félicitations
===============================================================

Ce script montre toutes les fonctionnalités du générateur d'affiches.
"""

import os
import time
from congratulations_poster import CongratulationsPoster
from config import get_config, validate_config


def demo_basic_usage():
    """Démonstration de l'utilisation basique."""
    print("🎯 Démonstration 1: Utilisation basique")
    print("-" * 50)
    
    # Utilisation simple avec les paramètres par défaut
    generator = CongratulationsPoster('templates/fond_defaut.png')
    
    # Générer une affiche avec les données d'exemple
    output_path = generator.generate_poster(
        'data/exemple_avec_photos.csv', 
        'Démonstration Basique'
    )
    
    print(f"✅ Affiche générée: {output_path}")
    return output_path


def demo_custom_configuration():
    """Démonstration avec configuration personnalisée."""
    print("\n🎨 Démonstration 2: Configuration personnalisée")
    print("-" * 50)
    
    # Créer un générateur avec des paramètres personnalisés
    generator = CongratulationsPoster(
        template_path='templates/fond_defaut.png',
        output_dir='demo_output'
    )
    
    # Personnaliser les couleurs
    generator.colors = {
        'text_primary': '#1A237E',      # Bleu marine
        'text_secondary': '#424242',     # Gris foncé
        'gold': '#FFD700',              # Or brillant
        'silver': '#C0C0C0',            # Argent
        'bronze': '#CD7F32'             # Bronze
    }
    
    # Personnaliser la mise en page
    generator.photo_size = 150
    generator.students_per_row = 3
    generator.margin = 100
    
    # Générer l'affiche
    output_path = generator.generate_poster(
        'data/exemple_avec_photos.csv',
        'Configuration Personnalisée'
    )
    
    print(f"✅ Affiche personnalisée générée: {output_path}")
    return output_path


def demo_multiple_classes():
    """Démonstration avec plusieurs classes."""
    print("\n📚 Démonstration 3: Traitement de plusieurs classes")
    print("-" * 50)
    
    generator = CongratulationsPoster(
        'templates/fond_defaut.png',
        'demo_multiple'
    )
    
    # Liste des classes à traiter
    classes = [
        ('data/6ème_a.csv', '6ème A'),
        ('data/5ème_b.csv', '5ème B'),
        ('data/4ème_c.csv', '4ème C')
    ]
    
    generated_files = []
    
    for csv_path, class_name in classes:
        if os.path.exists(csv_path):
            print(f"📝 Traitement de {class_name}...")
            output_path = generator.generate_poster(csv_path, class_name)
            if output_path:
                generated_files.append(output_path)
                print(f"   ✅ {output_path}")
        else:
            print(f"   ⚠️  Fichier non trouvé: {csv_path}")
    
    print(f"\n📊 Total: {len(generated_files)} affiches générées")
    return generated_files


def demo_performance_test():
    """Test de performance avec beaucoup d'élèves."""
    print("\n⚡ Démonstration 4: Test de performance")
    print("-" * 50)
    
    # Créer des données avec beaucoup d'élèves
    import pandas as pd
    
    num_students = 30
    large_data = {
        'Nom': [f'Élève {i+1:02d}' for i in range(num_students)],
        'Note': [12.0 + (i % 8) + (i % 3) * 0.5 for i in range(num_students)],
        'Classement': list(range(1, num_students + 1)),
        'Mention': [
            'Très Bien' if i < 8 else 
            'Bien' if i < 16 else 
            'Assez Bien' if i < 24 else 
            'Passable' 
            for i in range(num_students)
        ],
        'Photo': [f'photos/alice.jpg' for _ in range(num_students)]  # Réutiliser une photo existante
    }
    
    df = pd.DataFrame(large_data)
    csv_path = 'demo_large_class.csv'
    df.to_csv(csv_path, index=False)
    
    # Mesurer le temps de génération
    generator = CongratulationsPoster(
        'templates/fond_defaut.png',
        'demo_performance'
    )
    
    # Ajuster pour beaucoup d'élèves
    generator.students_per_row = 6
    generator.photo_size = 100
    
    start_time = time.time()
    output_path = generator.generate_poster(csv_path, f'Grande Classe ({num_students} élèves)')
    end_time = time.time()
    
    # Nettoyer
    os.remove(csv_path)
    
    print(f"✅ Affiche générée: {output_path}")
    print(f"⏱️  Temps de génération: {end_time - start_time:.2f} secondes")
    print(f"👥 Nombre d'élèves: {num_students}")
    
    if os.path.exists(output_path):
        file_size = os.path.getsize(output_path) / (1024 * 1024)  # MB
        print(f"📊 Taille du fichier: {file_size:.2f} MB")
    
    return output_path


def demo_configuration_validation():
    """Démonstration de la validation de configuration."""
    print("\n🔧 Démonstration 5: Validation de configuration")
    print("-" * 50)
    
    # Valider la configuration actuelle
    print("Validation de la configuration par défaut:")
    is_valid = validate_config()
    
    if is_valid:
        config = get_config()
        print(f"\n📋 Résumé de la configuration:")
        print(f"   - Dimensions: {config['poster']['width']}x{config['poster']['height']}")
        print(f"   - Taille des photos: {config['poster']['photo_size']}px")
        print(f"   - Élèves par ligne: {config['poster']['students_per_row']}")
        print(f"   - Mentions définies: {len(config['mentions'])}")
        print(f"   - Format de sortie: {config['output']['format']}")
    
    return is_valid


def demo_error_handling():
    """Démonstration de la gestion d'erreurs."""
    print("\n🛡️  Démonstration 6: Gestion d'erreurs")
    print("-" * 50)
    
    generator = CongratulationsPoster(
        'templates/fond_defaut.png',
        'demo_errors'
    )
    
    # Test avec un fichier CSV inexistant
    print("Test avec fichier CSV inexistant:")
    result = generator.generate_poster('fichier_inexistant.csv', 'Test Erreur')
    if not result:
        print("   ✅ Erreur gérée correctement")
    
    # Test avec un template inexistant
    print("\nTest avec template inexistant:")
    generator_bad_template = CongratulationsPoster(
        'template_inexistant.png',
        'demo_errors'
    )
    result = generator_bad_template.generate_poster('data/exemple_avec_photos.csv', 'Test Template')
    if result:
        print("   ✅ Template par défaut utilisé")
    
    return True


def generate_summary_report(all_outputs):
    """Génère un rapport de résumé."""
    print("\n📊 Rapport de résumé")
    print("=" * 50)
    
    total_files = len(all_outputs)
    total_size = 0
    
    print(f"📁 Fichiers générés: {total_files}")
    print("\n📋 Liste des fichiers:")
    
    for i, file_path in enumerate(all_outputs, 1):
        if os.path.exists(file_path):
            size = os.path.getsize(file_path) / 1024  # KB
            total_size += size
            print(f"   {i:2d}. {file_path} ({size:.1f} KB)")
        else:
            print(f"   {i:2d}. {file_path} (fichier non trouvé)")
    
    print(f"\n📊 Taille totale: {total_size / 1024:.2f} MB")
    
    # Statistiques par dossier
    folders = {}
    for file_path in all_outputs:
        if os.path.exists(file_path):
            folder = os.path.dirname(file_path)
            if folder not in folders:
                folders[folder] = 0
            folders[folder] += 1
    
    print(f"\n📂 Répartition par dossier:")
    for folder, count in folders.items():
        print(f"   - {folder}: {count} fichier(s)")


def main():
    """Fonction principale de démonstration."""
    print("🎓 DÉMONSTRATION COMPLÈTE DU GÉNÉRATEUR D'AFFICHES")
    print("=" * 60)
    print("Cette démonstration présente toutes les fonctionnalités du générateur.")
    print()
    
    all_outputs = []
    
    try:
        # Démonstration 1: Utilisation basique
        output1 = demo_basic_usage()
        if output1:
            all_outputs.append(output1)
        
        # Démonstration 2: Configuration personnalisée
        output2 = demo_custom_configuration()
        if output2:
            all_outputs.append(output2)
        
        # Démonstration 3: Plusieurs classes
        outputs3 = demo_multiple_classes()
        all_outputs.extend(outputs3)
        
        # Démonstration 4: Test de performance
        output4 = demo_performance_test()
        if output4:
            all_outputs.append(output4)
        
        # Démonstration 5: Validation de configuration
        demo_configuration_validation()
        
        # Démonstration 6: Gestion d'erreurs
        demo_error_handling()
        
        # Rapport final
        generate_summary_report(all_outputs)
        
        print("\n" + "=" * 60)
        print("🎉 Démonstration terminée avec succès!")
        print(f"📁 {len(all_outputs)} affiches générées au total")
        print("\n💡 Conseils:")
        print("   - Consultez les fichiers générés dans les différents dossiers")
        print("   - Modifiez config.py pour personnaliser l'apparence")
        print("   - Utilisez exemple_utilisation.py pour des cas d'usage avancés")
        
    except Exception as e:
        print(f"\n❌ Erreur lors de la démonstration: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
