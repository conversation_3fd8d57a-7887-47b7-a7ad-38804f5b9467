#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de test pour tous les fichiers CSV générés
================================================

Ce script teste la génération d'affiches avec tous les fichiers CSV de test.
"""

import os
import time
import pandas as pd
from congratulations_poster import Congratulations<PERSON><PERSON>er


def test_csv_file(csv_path, class_name, generator):
    """
    Teste la génération d'affiche pour un fichier CSV donné.
    
    Args:
        csv_path (str): Chemin vers le fichier CSV
        class_name (str): Nom de la classe
        generator (CongratulationsPoster): Instance du générateur
    
    Returns:
        dict: Résultats du test
    """
    print(f"\n📝 Test: {class_name}")
    print("-" * 50)
    
    if not os.path.exists(csv_path):
        print(f"❌ Fichier non trouvé: {csv_path}")
        return {'success': False, 'error': 'File not found'}
    
    try:
        # Charger et analyser les données
        df = pd.read_csv(csv_path)
        num_students = len(df)
        avg_note = df['Note'].mean()
        max_note = df['Note'].max()
        min_note = df['Note'].min()
        
        # Compter les mentions
        mentions_count = df['Mention'].value_counts().to_dict()
        
        print(f"👥 Nombre d'élèves: {num_students}")
        print(f"📊 Note moyenne: {avg_note:.2f}/20")
        print(f"🏆 Meilleure note: {max_note}/20")
        print(f"📉 Note la plus basse: {min_note}/20")
        print(f"🏅 Répartition des mentions:")
        for mention, count in mentions_count.items():
            print(f"   - {mention}: {count} élève(s)")
        
        # Générer l'affiche
        start_time = time.time()
        output_path = generator.generate_poster(csv_path, class_name)
        end_time = time.time()
        
        if output_path and os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / 1024  # KB
            print(f"✅ Affiche générée: {output_path}")
            print(f"⏱️  Temps de génération: {end_time - start_time:.2f}s")
            print(f"📊 Taille du fichier: {file_size:.1f} KB")
            
            return {
                'success': True,
                'output_path': output_path,
                'num_students': num_students,
                'avg_note': avg_note,
                'generation_time': end_time - start_time,
                'file_size_kb': file_size,
                'mentions': mentions_count
            }
        else:
            print("❌ Échec de la génération")
            return {'success': False, 'error': 'Generation failed'}
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return {'success': False, 'error': str(e)}


def main():
    """
    Fonction principale pour tester tous les fichiers CSV.
    """
    print("🧪 TEST DE TOUS LES FICHIERS CSV")
    print("=" * 60)
    
    # Initialiser le générateur
    generator = CongratulationsPoster(
        template_path='templates/fond_defaut.png',
        output_dir='test_csv_output'
    )
    
    # Liste des fichiers CSV à tester
    test_files = [
        ('data/test_classe_complete.csv', 'Classe Complète (20 élèves)'),
        ('data/test_petite_classe.csv', 'Petite Classe (6 élèves)'),
        ('data/test_excellence.csv', 'Classe d\'Excellence (8 élèves)'),
        ('data/test_classe_mixte.csv', 'Classe Mixte (12 élèves)'),
        ('data/test_grande_classe.csv', 'Grande Classe (25 élèves)'),
        ('data/test_notes_decimales.csv', 'Notes Décimales (20 élèves)'),
        ('data/exemple_avec_photos.csv', 'Exemple avec Photos (5 élèves)')
    ]
    
    results = []
    successful_tests = 0
    total_students = 0
    total_time = 0
    total_size = 0
    
    # Tester chaque fichier
    for csv_path, class_name in test_files:
        result = test_csv_file(csv_path, class_name, generator)
        results.append({
            'class_name': class_name,
            'csv_path': csv_path,
            **result
        })
        
        if result['success']:
            successful_tests += 1
            total_students += result['num_students']
            total_time += result['generation_time']
            total_size += result['file_size_kb']
    
    # Rapport final
    print("\n" + "=" * 60)
    print("📊 RAPPORT FINAL")
    print("=" * 60)
    
    print(f"✅ Tests réussis: {successful_tests}/{len(test_files)}")
    print(f"👥 Total d'élèves traités: {total_students}")
    print(f"⏱️  Temps total de génération: {total_time:.2f}s")
    print(f"📊 Taille totale des fichiers: {total_size:.1f} KB ({total_size/1024:.2f} MB)")
    
    if successful_tests > 0:
        print(f"📈 Temps moyen par affiche: {total_time/successful_tests:.2f}s")
        print(f"📈 Taille moyenne par affiche: {total_size/successful_tests:.1f} KB")
        print(f"📈 Élèves moyens par classe: {total_students/successful_tests:.1f}")
    
    # Détails par test
    print(f"\n📋 DÉTAILS PAR TEST:")
    print("-" * 60)
    
    for result in results:
        status = "✅" if result['success'] else "❌"
        print(f"{status} {result['class_name']}")
        
        if result['success']:
            print(f"   📁 {result['output_path']}")
            print(f"   👥 {result['num_students']} élèves | "
                  f"📊 {result['avg_note']:.1f}/20 | "
                  f"⏱️ {result['generation_time']:.2f}s | "
                  f"💾 {result['file_size_kb']:.1f}KB")
            
            # Afficher les mentions
            mentions_str = " | ".join([f"{m}: {c}" for m, c in result['mentions'].items()])
            print(f"   🏅 {mentions_str}")
        else:
            print(f"   ❌ Erreur: {result.get('error', 'Inconnue')}")
        print()
    
    # Recommandations
    print("💡 RECOMMANDATIONS:")
    print("-" * 60)
    
    if successful_tests == len(test_files):
        print("🎉 Tous les tests ont réussi ! Le générateur fonctionne parfaitement.")
        print("✅ Vous pouvez utiliser n'importe lequel de ces formats CSV.")
    else:
        print("⚠️  Certains tests ont échoué. Vérifiez les erreurs ci-dessus.")
    
    print("\n📁 Les affiches générées sont dans le dossier: test_csv_output/")
    print("🔧 Pour personnaliser l'apparence, modifiez le fichier config.py")
    print("📖 Consultez README.md pour plus d'informations")


if __name__ == "__main__":
    main()
