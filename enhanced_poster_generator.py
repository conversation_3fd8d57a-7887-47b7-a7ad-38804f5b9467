#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Générateur d'affiches amélioré avec support pour l'interface graphique
=====================================================================

Version améliorée du générateur avec support pour:
- Orientation portrait/paysage
- En-tête et pied de page personnalisables
- Styles de bordure variés
- Couleurs personnalisables
"""

import os
import pandas as pd
from PIL import Image, ImageDraw, ImageFont
import math
from typing import List, Tuple, Dict, Optional
from datetime import datetime


class EnhancedCongratulationsPoster:
    """
    Générateur d'affiches amélioré avec fonctionnalités étendues.
    """
    
    def __init__(self, template_path: str = None, output_dir: str = "output"):
        """
        Initialise le générateur amélioré.
        
        Args:
            template_path (str): Chemin vers l'image de fond
            output_dir (str): Dossier de sortie pour les affiches
        """
        self.template_path = template_path
        self.output_dir = output_dir
        self.create_output_directory()
        
        # Configuration par défaut
        self.poster_width = 1920
        self.poster_height = 1080
        self.photo_size = 120
        self.margin = 50
        self.students_per_row = 4
        
        # Configuration d'orientation
        self.orientation = "paysage"  # "paysage" ou "portrait"
        
        # Configuration de contenu
        self.header_text = "🎓 TABLEAU D'HONNEUR 🎓"
        self.header_enabled = True
        self.footer_text = "Félicitations à tous nos élèves !"
        self.footer_enabled = True
        self.school_name = "École Exemple"
        self.academic_year = "2024-2025"
        self.class_name = "Ma Classe"
        
        # Configuration de style
        self.background_color = "#F8F9FA"
        self.title_color = "#2C3E50"
        self.border_style = "moderne"  # "moderne", "classique", "aucune"
        
        # Couleurs des mentions
        self.colors = {
            'text_primary': '#2C3E50',
            'text_secondary': '#7F8C8D',
            'gold': '#F1C40F',
            'silver': '#BDC3C7',
            'bronze': '#CD7F32',
            'background': self.background_color,
            'title': self.title_color
        }
    
    def create_output_directory(self):
        """Crée le dossier de sortie s'il n'existe pas."""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def set_orientation(self, orientation: str):
        """
        Définit l'orientation de l'affiche.
        
        Args:
            orientation (str): "paysage" ou "portrait"
        """
        self.orientation = orientation
        if orientation == "portrait":
            # S'assurer que la hauteur est supérieure à la largeur
            if self.poster_width > self.poster_height:
                self.poster_width, self.poster_height = self.poster_height, self.poster_width
        else:  # paysage
            # S'assurer que la largeur est supérieure à la hauteur
            if self.poster_height > self.poster_width:
                self.poster_width, self.poster_height = self.poster_height, self.poster_width
    
    def set_dimensions(self, width: int, height: int):
        """
        Définit les dimensions de l'affiche.
        
        Args:
            width (int): Largeur en pixels
            height (int): Hauteur en pixels
        """
        self.poster_width = width
        self.poster_height = height
    
    def set_content(self, header_text: str = None, footer_text: str = None, 
                   school_name: str = None, academic_year: str = None,
                   header_enabled: bool = True, footer_enabled: bool = True):
        """
        Configure le contenu de l'affiche.
        
        Args:
            header_text (str): Texte de l'en-tête
            footer_text (str): Texte du pied de page
            school_name (str): Nom de l'école
            academic_year (str): Année scolaire
            header_enabled (bool): Activer l'en-tête
            footer_enabled (bool): Activer le pied de page
        """
        if header_text is not None:
            self.header_text = header_text
        if footer_text is not None:
            self.footer_text = footer_text
        if school_name is not None:
            self.school_name = school_name
        if academic_year is not None:
            self.academic_year = academic_year
        
        self.header_enabled = header_enabled
        self.footer_enabled = footer_enabled
    
    def set_style(self, background_color: str = None, title_color: str = None, 
                 border_style: str = None):
        """
        Configure le style de l'affiche.
        
        Args:
            background_color (str): Couleur de fond (hex)
            title_color (str): Couleur du titre (hex)
            border_style (str): Style de bordure
        """
        if background_color is not None:
            self.background_color = background_color
            self.colors['background'] = background_color
        if title_color is not None:
            self.title_color = title_color
            self.colors['title'] = title_color
        if border_style is not None:
            self.border_style = border_style
    
    def load_students_data(self, csv_path: str) -> pd.DataFrame:
        """
        Charge les données des élèves depuis un fichier CSV.
        
        Args:
            csv_path (str): Chemin vers le fichier CSV
            
        Returns:
            pd.DataFrame: Données des élèves
        """
        try:
            df = pd.read_csv(csv_path, encoding='utf-8')
            required_columns = ['Nom', 'Note', 'Classement', 'Mention', 'Photo']
            
            for col in required_columns:
                if col not in df.columns:
                    raise ValueError(f"Colonne manquante: {col}")
            
            # Trier par classement
            df = df.sort_values('Classement')
            return df
            
        except Exception as e:
            print(f"Erreur lors du chargement des données: {e}")
            return pd.DataFrame()
    
    def create_background(self) -> Image.Image:
        """
        Crée l'arrière-plan de l'affiche.
        
        Returns:
            Image.Image: Image d'arrière-plan
        """
        if self.template_path and os.path.exists(self.template_path):
            # Charger le template existant
            background = Image.open(self.template_path).convert('RGBA')
            background = background.resize((self.poster_width, self.poster_height), Image.Resampling.LANCZOS)
        else:
            # Créer un arrière-plan par défaut
            background = self.create_default_background()
        
        return background
    
    def create_default_background(self) -> Image.Image:
        """
        Crée un arrière-plan par défaut avec dégradé et bordures.
        
        Returns:
            Image.Image: Image d'arrière-plan par défaut
        """
        # Convertir la couleur hex en RGB
        bg_color = self.hex_to_rgb(self.background_color)
        
        # Créer l'image de base
        background = Image.new('RGB', (self.poster_width, self.poster_height), bg_color)
        draw = ImageDraw.Draw(background)
        
        # Ajouter un dégradé subtil
        for y in range(self.poster_height):
            alpha = y / self.poster_height
            gradient_color = tuple(int(c + (255 - c) * alpha * 0.1) for c in bg_color)
            draw.line([(0, y), (self.poster_width, y)], fill=gradient_color)
        
        # Ajouter des bordures selon le style
        self.add_border(draw)
        
        return background
    
    def hex_to_rgb(self, hex_color: str) -> tuple:
        """
        Convertit une couleur hexadécimale en RGB.
        
        Args:
            hex_color (str): Couleur au format #RRGGBB
            
        Returns:
            tuple: (R, G, B)
        """
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
    
    def add_border(self, draw: ImageDraw.Draw):
        """
        Ajoute une bordure selon le style choisi.
        
        Args:
            draw (ImageDraw.Draw): Objet de dessin
        """
        if self.border_style == "aucune":
            return
        
        border_color = self.hex_to_rgb(self.title_color)
        
        if self.border_style == "moderne":
            # Bordure moderne avec coins arrondis simulés
            border_width = 8
            
            # Bordures principales
            draw.rectangle([0, 0, self.poster_width, border_width], fill=border_color)
            draw.rectangle([0, self.poster_height-border_width, self.poster_width, self.poster_height], fill=border_color)
            draw.rectangle([0, 0, border_width, self.poster_height], fill=border_color)
            draw.rectangle([self.poster_width-border_width, 0, self.poster_width, self.poster_height], fill=border_color)
            
            # Éléments décoratifs dans les coins
            corner_size = 40
            for corner in [(border_width, border_width), 
                          (self.poster_width-border_width-corner_size, border_width),
                          (border_width, self.poster_height-border_width-corner_size),
                          (self.poster_width-border_width-corner_size, self.poster_height-border_width-corner_size)]:
                draw.rectangle([corner[0], corner[1], corner[0]+corner_size, corner[1]+4], fill=border_color)
        
        elif self.border_style == "classique":
            # Bordure classique simple
            border_width = 12
            draw.rectangle([0, 0, self.poster_width, border_width], fill=border_color)
            draw.rectangle([0, self.poster_height-border_width, self.poster_width, self.poster_height], fill=border_color)
            draw.rectangle([0, 0, border_width, self.poster_height], fill=border_color)
            draw.rectangle([self.poster_width-border_width, 0, self.poster_width, self.poster_height], fill=border_color)
            
            # Bordure intérieure
            inner_border = 4
            inner_margin = border_width + 8
            draw.rectangle([inner_margin, inner_margin, self.poster_width-inner_margin, inner_margin+inner_border], fill=border_color)
            draw.rectangle([inner_margin, self.poster_height-inner_margin-inner_border, self.poster_width-inner_margin, self.poster_height-inner_margin], fill=border_color)
            draw.rectangle([inner_margin, inner_margin, inner_margin+inner_border, self.poster_height-inner_margin], fill=border_color)
            draw.rectangle([self.poster_width-inner_margin-inner_border, inner_margin, self.poster_width-inner_margin, self.poster_height-inner_margin], fill=border_color)
    
    def add_enhanced_header(self, draw: ImageDraw.Draw):
        """
        Ajoute un en-tête amélioré avec informations de l'école.
        
        Args:
            draw (ImageDraw.Draw): Objet de dessin
        """
        if not self.header_enabled:
            return
        
        try:
            # Police pour le titre principal
            title_font = ImageFont.truetype("arial.ttf", 48)
            subtitle_font = ImageFont.truetype("arial.ttf", 24)
            info_font = ImageFont.truetype("arial.ttf", 18)
        except:
            title_font = ImageFont.load_default()
            subtitle_font = ImageFont.load_default()
            info_font = ImageFont.load_default()
        
        title_color = self.hex_to_rgb(self.title_color)
        
        # Titre principal
        header_text = self.header_text.format(class_name=self.class_name)
        bbox = draw.textbbox((0, 0), header_text, font=title_font)
        title_width = bbox[2] - bbox[0]
        title_x = (self.poster_width - title_width) // 2
        title_y = 40
        
        # Ombre du titre
        draw.text((title_x + 3, title_y + 3), header_text, fill=(128, 128, 128), font=title_font)
        # Titre principal
        draw.text((title_x, title_y), header_text, fill=title_color, font=title_font)
        
        # Sous-titre avec classe
        if self.class_name:
            class_text = f"Classe: {self.class_name}"
            bbox = draw.textbbox((0, 0), class_text, font=subtitle_font)
            class_width = bbox[2] - bbox[0]
            class_x = (self.poster_width - class_width) // 2
            class_y = title_y + 60
            
            draw.text((class_x, class_y), class_text, fill=title_color, font=subtitle_font)
        
        # Informations école et année
        info_y = title_y + 100
        if self.school_name:
            school_text = f"🏛️ {self.school_name}"
            bbox = draw.textbbox((0, 0), school_text, font=info_font)
            school_width = bbox[2] - bbox[0]
            school_x = (self.poster_width - school_width) // 2 - 100
            
            draw.text((school_x, info_y), school_text, fill=title_color, font=info_font)
        
        if self.academic_year:
            year_text = f"📅 Année {self.academic_year}"
            bbox = draw.textbbox((0, 0), year_text, font=info_font)
            year_x = (self.poster_width + 100) // 2
            
            draw.text((year_x, info_y), year_text, fill=title_color, font=info_font)
    
    def add_enhanced_footer(self, draw: ImageDraw.Draw):
        """
        Ajoute un pied de page amélioré.
        
        Args:
            draw (ImageDraw.Draw): Objet de dessin
        """
        if not self.footer_enabled:
            return
        
        try:
            footer_font = ImageFont.truetype("arial.ttf", 20)
            date_font = ImageFont.truetype("arial.ttf", 16)
        except:
            footer_font = ImageFont.load_default()
            date_font = ImageFont.load_default()
        
        title_color = self.hex_to_rgb(self.title_color)
        
        # Texte du pied de page
        bbox = draw.textbbox((0, 0), self.footer_text, font=footer_font)
        footer_width = bbox[2] - bbox[0]
        footer_x = (self.poster_width - footer_width) // 2
        footer_y = self.poster_height - 80
        
        draw.text((footer_x, footer_y), self.footer_text, fill=title_color, font=footer_font)
        
        # Date de génération
        date_text = f"Généré le {datetime.now().strftime('%d/%m/%Y')}"
        bbox = draw.textbbox((0, 0), date_text, font=date_font)
        date_width = bbox[2] - bbox[0]
        date_x = (self.poster_width - date_width) // 2
        date_y = footer_y + 30
        
        draw.text((date_x, date_y), date_text, fill=(128, 128, 128), font=date_font)
