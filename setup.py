#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script d'installation et de configuration du générateur d'affiches
================================================================

Ce script configure automatiquement l'environnement pour le générateur d'affiches.
"""

import os
import sys
import subprocess
from pathlib import Path


def check_python_version():
    """Vérifie que la version de Python est compatible."""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 ou plus récent est requis.")
        print(f"   Version actuelle: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} détecté")
    return True


def install_dependencies():
    """Installe les dépendances requises."""
    print("\n📦 Installation des dépendances...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ Dépendances installées avec succès")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Erreur lors de l'installation des dépendances: {e}")
        return False


def create_directory_structure():
    """Crée la structure de dossiers nécessaire."""
    print("\n📁 Création de la structure de dossiers...")
    
    directories = [
        'data',
        'photos', 
        'templates',
        'output',
        'decorations',
        'examples'
    ]
    
    created_dirs = []
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            created_dirs.append(directory)
            print(f"   ✅ Dossier créé: {directory}/")
        else:
            print(f"   📂 Dossier existant: {directory}/")
    
    return created_dirs


def create_sample_photos():
    """Crée des photos d'exemple simples."""
    print("\n🖼️  Création de photos d'exemple...")
    
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        names = ['alice', 'bob', 'claire', 'david', 'emma']
        colors = [
            (100, 149, 237),  # Bleu
            (255, 182, 193),  # Rose
            (144, 238, 144),  # Vert clair
            (255, 218, 185),  # Pêche
            (221, 160, 221),  # Prune
        ]
        
        for i, (name, color) in enumerate(zip(names, colors)):
            # Créer une image simple avec initiales
            img = Image.new('RGB', (200, 200), color)
            draw = ImageDraw.Draw(img)
            
            # Ajouter les initiales
            initials = name[0].upper()
            try:
                font = ImageFont.truetype("arial.ttf", 80)
            except:
                font = ImageFont.load_default()
            
            # Centrer le texte
            bbox = draw.textbbox((0, 0), initials, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            x = (200 - text_width) // 2
            y = (200 - text_height) // 2
            
            draw.text((x, y), initials, fill='white', font=font)
            
            # Sauvegarder
            photo_path = f"photos/{name}.jpg"
            img.save(photo_path, 'JPEG')
            print(f"   ✅ Photo créée: {photo_path}")
        
        return True
        
    except ImportError:
        print("   ⚠️  Pillow non installé, photos d'exemple non créées")
        return False
    except Exception as e:
        print(f"   ❌ Erreur lors de la création des photos: {e}")
        return False


def create_example_csv():
    """Crée un fichier CSV d'exemple avec de vraies photos."""
    print("\n📊 Création d'un fichier CSV d'exemple...")
    
    try:
        import pandas as pd
        
        # Données d'exemple avec les photos créées
        sample_data = {
            'Nom': [
                'Alice Martin', 'Bob Dupont', 'Claire Rousseau', 
                'David Moreau', 'Emma Bernard'
            ],
            'Note': [18.5, 17.2, 16.8, 16.5, 15.9],
            'Classement': [1, 2, 3, 4, 5],
            'Mention': [
                'Très Bien', 'Très Bien', 'Bien', 'Bien', 'Bien'
            ],
            'Photo': [
                'photos/alice.jpg', 'photos/bob.jpg', 'photos/claire.jpg',
                'photos/david.jpg', 'photos/emma.jpg'
            ]
        }
        
        df = pd.DataFrame(sample_data)
        csv_path = 'data/exemple_avec_photos.csv'
        df.to_csv(csv_path, index=False, encoding='utf-8')
        print(f"   ✅ Fichier CSV créé: {csv_path}")
        return True
        
    except ImportError:
        print("   ⚠️  Pandas non installé, CSV d'exemple non créé")
        return False
    except Exception as e:
        print(f"   ❌ Erreur lors de la création du CSV: {e}")
        return False


def test_installation():
    """Teste que l'installation fonctionne correctement."""
    print("\n🧪 Test de l'installation...")
    
    try:
        # Importer le module principal
        from congratulations_poster import CongratulationsPoster
        print("   ✅ Module principal importé avec succès")
        
        # Tester la création d'une instance
        generator = CongratulationsPoster('templates/fond_defaut.png')
        print("   ✅ Générateur initialisé avec succès")
        
        # Tester la validation de la configuration
        from config import validate_config
        if validate_config():
            print("   ✅ Configuration validée")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ Erreur d'importation: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Erreur lors du test: {e}")
        return False


def create_quick_start_guide():
    """Crée un guide de démarrage rapide."""
    print("\n📖 Création du guide de démarrage rapide...")
    
    guide_content = """# 🚀 Guide de Démarrage Rapide

## Première utilisation

1. **Générer une affiche d'exemple** :
   ```bash
   python congratulations_poster.py
   ```

2. **Utiliser vos propres données** :
   - Placez vos photos dans le dossier `photos/`
   - Créez votre fichier CSV dans le dossier `data/`
   - Modifiez le script ou utilisez l'exemple avancé

3. **Personnaliser l'apparence** :
   - Modifiez le fichier `config.py`
   - Changez les couleurs, polices, dimensions, etc.

## Exemples de commandes

```python
# Utilisation basique
from congratulations_poster import CongratulationsPoster

generator = CongratulationsPoster('templates/fond_defaut.png')
generator.generate_poster('data/ma_classe.csv', 'Ma Classe')
```

```bash
# Exemple avancé avec plusieurs classes
python exemple_utilisation.py
```

```bash
# Tests
python test_poster_generator.py
```

## Structure des fichiers

- `data/` : Fichiers CSV avec les données des élèves
- `photos/` : Photos des élèves (JPG, PNG)
- `templates/` : Images de fond pour les affiches
- `output/` : Affiches générées
- `config.py` : Configuration personnalisable

## Format du CSV

| Nom | Note | Classement | Mention | Photo |
|-----|------|------------|---------|-------|
| Alice Martin | 18.5 | 1 | Très Bien | photos/alice.jpg |

## Aide

- Consultez le `README.md` pour plus de détails
- Modifiez `config.py` pour personnaliser
- Utilisez `test_poster_generator.py` pour vérifier le fonctionnement
"""
    
    try:
        with open('QUICK_START.md', 'w', encoding='utf-8') as f:
            f.write(guide_content)
        print("   ✅ Guide créé: QUICK_START.md")
        return True
    except Exception as e:
        print(f"   ❌ Erreur lors de la création du guide: {e}")
        return False


def main():
    """Fonction principale d'installation."""
    print("🎓 Installation du Générateur d'Affiches de Félicitations")
    print("=" * 60)
    
    # Vérifications préliminaires
    if not check_python_version():
        return False
    
    # Installation des dépendances
    if not install_dependencies():
        print("\n❌ Installation échouée à l'étape des dépendances")
        return False
    
    # Création de la structure
    created_dirs = create_directory_structure()
    
    # Création des exemples
    create_sample_photos()
    create_example_csv()
    
    # Test de l'installation
    if not test_installation():
        print("\n❌ Installation échouée lors des tests")
        return False
    
    # Guide de démarrage
    create_quick_start_guide()
    
    # Résumé final
    print("\n" + "=" * 60)
    print("🎉 Installation terminée avec succès!")
    print("\n📋 Résumé:")
    print("   ✅ Dépendances installées")
    print("   ✅ Structure de dossiers créée")
    print("   ✅ Exemples générés")
    print("   ✅ Tests réussis")
    
    print("\n🚀 Pour commencer:")
    print("   1. Exécutez: python congratulations_poster.py")
    print("   2. Consultez: QUICK_START.md")
    print("   3. Personnalisez: config.py")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
