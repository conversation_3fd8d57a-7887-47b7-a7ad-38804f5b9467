#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Créateur de fichiers CSV personnalisés pour le générateur d'affiches
===================================================================

Ce script aide à créer facilement des fichiers CSV conformes au générateur.
"""

import pandas as pd
import os
from datetime import datetime


def creer_csv_interactif():
    """
    Crée un fichier CSV de manière interactive.
    """
    print("🎓 Créateur de CSV Personnalisé")
    print("=" * 40)
    
    # Demander le nom de la classe
    nom_classe = input("📚 Nom de la classe (ex: 6ème A): ").strip()
    if not nom_classe:
        nom_classe = "Ma Classe"
    
    # Demander le nombre d'élèves
    while True:
        try:
            nb_eleves = int(input("👥 Nombre d'élèves: "))
            if nb_eleves > 0:
                break
            else:
                print("❌ Le nombre doit être positif")
        except ValueError:
            print("❌ Veuillez entrer un nombre valide")
    
    print(f"\n📝 Saisie des données pour {nb_eleves} élève(s):")
    print("💡 Appuyez sur Entrée pour utiliser les valeurs par défaut")
    
    eleves = []
    
    for i in range(nb_eleves):
        print(f"\n--- Élève {i+1} ---")
        
        # Nom
        nom = input(f"Nom (défaut: Élève {i+1}): ").strip()
        if not nom:
            nom = f"Élève {i+1}"
        
        # Note
        while True:
            note_str = input(f"Note sur 20 (défaut: {20-i*0.5:.1f}): ").strip()
            if not note_str:
                note = max(10, 20 - i * 0.5)
                break
            try:
                note = float(note_str)
                if 0 <= note <= 20:
                    break
                else:
                    print("❌ La note doit être entre 0 et 20")
            except ValueError:
                print("❌ Veuillez entrer un nombre valide")
        
        # Mention automatique selon la note
        if note >= 16:
            mention = "Très Bien"
        elif note >= 14:
            mention = "Bien"
        elif note >= 12:
            mention = "Assez Bien"
        else:
            mention = "Passable"
        
        # Photo
        photo_defaut = f"photos/{nom.lower().replace(' ', '_')}.jpg"
        photo = input(f"Chemin photo (défaut: {photo_defaut}): ").strip()
        if not photo:
            photo = photo_defaut
        
        eleves.append({
            'Nom': nom,
            'Note': note,
            'Classement': i + 1,
            'Mention': mention,
            'Photo': photo
        })
    
    # Trier par note décroissante et réassigner les classements
    eleves.sort(key=lambda x: x['Note'], reverse=True)
    for i, eleve in enumerate(eleves):
        eleve['Classement'] = i + 1
    
    # Créer le DataFrame
    df = pd.DataFrame(eleves)
    
    # Nom du fichier
    nom_fichier = f"data/{nom_classe.lower().replace(' ', '_').replace('è', 'e').replace('é', 'e')}.csv"
    
    # Sauvegarder
    df.to_csv(nom_fichier, index=False, encoding='utf-8')
    
    print(f"\n✅ Fichier CSV créé: {nom_fichier}")
    
    # Afficher un aperçu
    print(f"\n📊 Aperçu des données:")
    print(df.to_string(index=False))
    
    return nom_fichier, nom_classe


def creer_csv_rapide(nom_classe, nb_eleves, base_note=18):
    """
    Crée rapidement un fichier CSV avec des données générées automatiquement.
    
    Args:
        nom_classe (str): Nom de la classe
        nb_eleves (int): Nombre d'élèves
        base_note (float): Note de base (la plus haute)
    
    Returns:
        str: Chemin du fichier créé
    """
    # Prénoms français courants
    prenoms = [
        "Alice", "Antoine", "Camille", "David", "Emma", "Florian", "Gabrielle", "Hugo",
        "Inès", "Jules", "Léa", "Maxime", "Noémie", "Oscar", "Pauline", "Quentin",
        "Rose", "Simon", "Théa", "Victor", "Zoé", "Baptiste", "Clara", "Dylan",
        "Élise", "Félix", "Grace", "Henri", "Iris", "Julien", "Karine", "Louis"
    ]
    
    # Noms de famille français
    noms = [
        "Martin", "Bernard", "Dubois", "Thomas", "Robert", "Richard", "Petit", "Durand",
        "Leroy", "Moreau", "Simon", "Laurent", "Lefebvre", "Michel", "Garcia", "David",
        "Bertrand", "Roux", "Vincent", "Fournier", "Morel", "Girard", "André", "Lefevre",
        "Mercier", "Dupont", "Lambert", "Bonnet", "François", "Martinez", "Legrand", "Garnier"
    ]
    
    eleves = []
    
    for i in range(nb_eleves):
        # Générer un nom unique
        prenom = prenoms[i % len(prenoms)]
        nom = noms[i % len(noms)]
        nom_complet = f"{prenom} {nom}"
        
        # Générer une note décroissante avec un peu de variation
        note = max(10, base_note - i * 0.3 + (i % 3) * 0.2)
        note = round(note, 1)
        
        # Déterminer la mention
        if note >= 16:
            mention = "Très Bien"
        elif note >= 14:
            mention = "Bien"
        elif note >= 12:
            mention = "Assez Bien"
        else:
            mention = "Passable"
        
        # Chemin de la photo
        photo = f"photos/{prenom.lower()}_{nom.lower()}.jpg"
        
        eleves.append({
            'Nom': nom_complet,
            'Note': note,
            'Classement': i + 1,
            'Mention': mention,
            'Photo': photo
        })
    
    # Créer le DataFrame
    df = pd.DataFrame(eleves)
    
    # Nom du fichier avec timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M")
    nom_fichier = f"data/{nom_classe.lower().replace(' ', '_')}_{timestamp}.csv"
    
    # Sauvegarder
    df.to_csv(nom_fichier, index=False, encoding='utf-8')
    
    return nom_fichier


def tester_csv_cree(nom_fichier, nom_classe):
    """
    Teste immédiatement le fichier CSV créé.
    
    Args:
        nom_fichier (str): Chemin du fichier CSV
        nom_classe (str): Nom de la classe
    """
    print(f"\n🧪 Test du fichier CSV créé...")
    
    try:
        from congratulations_poster import CongratulationsPoster
        
        # Créer le générateur
        generator = CongratulationsPoster(
            'templates/fond_defaut.png',
            'csv_personnalise_output'
        )
        
        # Générer l'affiche
        output_path = generator.generate_poster(nom_fichier, nom_classe)
        
        if output_path and os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / 1024  # KB
            print(f"✅ Affiche générée avec succès!")
            print(f"📁 Fichier: {output_path}")
            print(f"💾 Taille: {file_size:.1f} KB")
            return True
        else:
            print("❌ Échec de la génération")
            return False
            
    except ImportError:
        print("⚠️  Module congratulations_poster non trouvé")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False


def main():
    """
    Fonction principale avec menu interactif.
    """
    print("🎓 CRÉATEUR DE FICHIERS CSV PERSONNALISÉS")
    print("=" * 50)
    print("Choisissez une option:")
    print("1. Création interactive (saisie manuelle)")
    print("2. Création rapide (génération automatique)")
    print("3. Quitter")
    
    while True:
        choix = input("\nVotre choix (1-3): ").strip()
        
        if choix == "1":
            # Création interactive
            nom_fichier, nom_classe = creer_csv_interactif()
            
            # Proposer de tester
            test = input("\n🧪 Voulez-vous tester immédiatement? (o/N): ").strip().lower()
            if test in ['o', 'oui', 'y', 'yes']:
                tester_csv_cree(nom_fichier, nom_classe)
            
            break
            
        elif choix == "2":
            # Création rapide
            print("\n🚀 Création rapide")
            
            nom_classe = input("📚 Nom de la classe: ").strip()
            if not nom_classe:
                nom_classe = "Classe Rapide"
            
            while True:
                try:
                    nb_eleves = int(input("👥 Nombre d'élèves: "))
                    if nb_eleves > 0:
                        break
                    else:
                        print("❌ Le nombre doit être positif")
                except ValueError:
                    print("❌ Veuillez entrer un nombre valide")
            
            base_note_str = input("📊 Note de base (défaut: 18): ").strip()
            base_note = 18 if not base_note_str else float(base_note_str)
            
            nom_fichier = creer_csv_rapide(nom_classe, nb_eleves, base_note)
            
            print(f"\n✅ Fichier CSV créé: {nom_fichier}")
            
            # Afficher un aperçu
            df = pd.read_csv(nom_fichier)
            print(f"\n📊 Aperçu des données:")
            print(df.head(10).to_string(index=False))
            if len(df) > 10:
                print(f"... et {len(df) - 10} autres élèves")
            
            # Proposer de tester
            test = input("\n🧪 Voulez-vous tester immédiatement? (o/N): ").strip().lower()
            if test in ['o', 'oui', 'y', 'yes']:
                tester_csv_cree(nom_fichier, nom_classe)
            
            break
            
        elif choix == "3":
            print("👋 Au revoir!")
            break
            
        else:
            print("❌ Choix invalide, veuillez choisir 1, 2 ou 3")
    
    print(f"\n💡 Conseils:")
    print(f"   - Placez vos photos dans le dossier 'photos/'")
    print(f"   - Utilisez 'python congratulations_poster.py' pour générer des affiches")
    print(f"   - Consultez 'GUIDE_CSV.md' pour plus d'informations")


if __name__ == "__main__":
    main()
