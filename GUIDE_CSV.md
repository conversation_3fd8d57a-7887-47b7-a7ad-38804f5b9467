# 📊 Guide des Fichiers CSV - Générateur d'Affiches

## 📋 Format CSV Requis

### Colonnes Obligatoires

| Colonne | Type | Description | Exemple |
|---------|------|-------------|---------|
| **Nom** | Texte | Nom complet de l'élève | "Alice Martin" |
| **Note** | Nombre | Note sur 20 (décimales autorisées) | 18.5 |
| **Classement** | Entier | Position dans le classement | 1 |
| **Mention** | Texte | Mention obtenue | "Très Bien" |
| **Photo** | Texte | Chemin vers la photo | "photos/alice.jpg" |

### Mentions Supportées

- **"Très Bien"** - Couleur or 🥇
- **"Bien"** - Couleur argent 🥈  
- **"Assez Bien"** - Couleur bronze 🥉
- **"Passable"** - Couleur grise 📝

## 📁 Fichiers CSV de Test Fournis

### 1. `test_classe_complete.csv` - Classe Standard (20 élèves)
```csv
Nom,Note,Classement,Mention,Photo
Amé<PERSON>,19.5,1,<PERSON>r<PERSON>,photos/amelie.jpg
<PERSON>,18.8,2,Très Bien,photos/baptiste.jpg
...
```
- **Usage** : Test avec une classe de taille moyenne
- **Répartition** : 6 Très Bien, 6 Bien, 6 Assez Bien, 2 Passable
- **Notes** : 12.9 à 19.5

### 2. `test_petite_classe.csv` - Petite Classe (6 élèves)
```csv
Nom,Note,Classement,Mention,Photo
Sophie Delacroix,19.0,1,Très Bien,photos/sophie.jpg
Antoine Lemaire,18.5,2,Très Bien,photos/antoine.jpg
...
```
- **Usage** : Test avec peu d'élèves
- **Répartition** : 3 Très Bien, 3 Bien
- **Notes** : 15.5 à 19.0

### 3. `test_excellence.csv` - Classe d'Excellence (8 élèves)
```csv
Nom,Note,Classement,Mention,Photo
Marie-Claire Dubois,20.0,1,Très Bien,photos/marie_claire.jpg
Jean-Baptiste Moreau,19.8,2,Très Bien,photos/jean_baptiste.jpg
...
```
- **Usage** : Test avec uniquement des excellents résultats
- **Répartition** : 8 Très Bien
- **Notes** : 18.2 à 20.0

### 4. `test_classe_mixte.csv` - Classe Multiculturelle (12 élèves)
```csv
Nom,Note,Classement,Mention,Photo
Yasmine El-Mansouri,18.7,1,Très Bien,photos/yasmine.jpg
Kevin N'Guessan,18.3,2,Très Bien,photos/kevin.jpg
...
```
- **Usage** : Test avec noms internationaux
- **Répartition** : 4 Très Bien, 4 Bien, 3 Assez Bien, 1 Passable
- **Notes** : 13.4 à 18.7

### 5. `test_grande_classe.csv` - Grande Classe (25 élèves)
```csv
Nom,Note,Classement,Mention,Photo
Adèle Moreau,19.2,1,Très Bien,photos/adele.jpg
Benjamin Leroy,18.9,2,Très Bien,photos/benjamin.jpg
...
```
- **Usage** : Test de performance avec beaucoup d'élèves
- **Répartition** : 8 Très Bien, 6 Bien, 6 Assez Bien, 5 Passable
- **Notes** : 12.0 à 19.2

### 6. `test_notes_decimales.csv` - Notes Précises (20 élèves)
```csv
Nom,Note,Classement,Mention,Photo
Alice Beaumont,19.75,1,Très Bien,photos/alice_b.jpg
Bruno Castellane,19.25,2,Très Bien,photos/bruno.jpg
...
```
- **Usage** : Test avec notes décimales précises
- **Répartition** : 8 Très Bien, 5 Bien, 4 Assez Bien, 3 Passable
- **Notes** : 12.75 à 19.75

## 🧪 Résultats des Tests

### Performances Mesurées
- **Temps de génération** : 0.10s à 0.28s selon la taille
- **Taille des fichiers** : 53.8 KB à 84.3 KB
- **Capacité testée** : Jusqu'à 25 élèves par affiche

### Statistiques Globales
- ✅ **7/7 tests réussis** (100% de succès)
- 👥 **96 élèves traités** au total
- ⏱️ **Temps moyen** : 0.18s par affiche
- 💾 **Taille moyenne** : 70.1 KB par affiche

## 📝 Créer Votre Propre Fichier CSV

### Méthode 1 : Excel/LibreOffice
1. Créez un nouveau tableur
2. Ajoutez les colonnes : Nom, Note, Classement, Mention, Photo
3. Remplissez vos données
4. Sauvegardez au format CSV (UTF-8)

### Méthode 2 : Éditeur de texte
```csv
Nom,Note,Classement,Mention,Photo
Votre Élève 1,18.0,1,Très Bien,photos/eleve1.jpg
Votre Élève 2,16.5,2,Bien,photos/eleve2.jpg
```

### Méthode 3 : Python
```python
import pandas as pd

data = {
    'Nom': ['Alice Martin', 'Bob Dupont'],
    'Note': [18.5, 17.2],
    'Classement': [1, 2],
    'Mention': ['Très Bien', 'Très Bien'],
    'Photo': ['photos/alice.jpg', 'photos/bob.jpg']
}

df = pd.DataFrame(data)
df.to_csv('ma_classe.csv', index=False, encoding='utf-8')
```

## ⚠️ Points d'Attention

### Encodage
- **Utilisez UTF-8** pour les caractères accentués
- Évitez les caractères spéciaux dans les noms de fichiers

### Photos
- Les photos manquantes sont **automatiquement remplacées** par des placeholders
- Formats supportés : JPG, PNG, BMP, GIF
- Résolution recommandée : 200x200 pixels minimum

### Notes
- **Plage acceptée** : 0 à 20
- **Décimales autorisées** : 0.25, 0.5, 0.75, etc.
- **Format** : Utilisez le point (.) comme séparateur décimal

### Mentions
- **Respectez l'orthographe exacte** :
  - "Très Bien" (avec accent)
  - "Bien"
  - "Assez Bien"
  - "Passable"

## 🔧 Validation de Votre CSV

### Script de Validation
```python
import pandas as pd

def valider_csv(chemin_csv):
    try:
        df = pd.read_csv(chemin_csv)
        
        # Vérifier les colonnes
        colonnes_requises = ['Nom', 'Note', 'Classement', 'Mention', 'Photo']
        for col in colonnes_requises:
            if col not in df.columns:
                print(f"❌ Colonne manquante: {col}")
                return False
        
        # Vérifier les notes
        if df['Note'].min() < 0 or df['Note'].max() > 20:
            print("❌ Notes hors limites (0-20)")
            return False
        
        # Vérifier les mentions
        mentions_valides = ['Très Bien', 'Bien', 'Assez Bien', 'Passable']
        mentions_invalides = df[~df['Mention'].isin(mentions_valides)]['Mention'].unique()
        if len(mentions_invalides) > 0:
            print(f"❌ Mentions invalides: {mentions_invalides}")
            return False
        
        print("✅ CSV valide!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

# Utilisation
valider_csv('mon_fichier.csv')
```

## 🚀 Test Rapide

Pour tester rapidement un nouveau fichier CSV :

```bash
# Tester votre fichier
python -c "
from congratulations_poster import CongratulationsPoster
g = CongratulationsPoster('templates/fond_defaut.png')
g.generate_poster('data/votre_fichier.csv', 'Test')
"
```

## 📊 Conseils d'Optimisation

### Pour de Grandes Classes (20+ élèves)
- Réduisez la taille des photos
- Utilisez des noms courts si possible
- Testez d'abord avec un échantillon

### Pour de Petites Classes (< 10 élèves)
- Augmentez la taille des photos dans config.py
- Réduisez le nombre d'élèves par ligne
- Ajustez les marges pour un meilleur centrage

---

**💡 Astuce** : Utilisez `test_csv_files.py` pour tester tous vos fichiers CSV en une fois !
