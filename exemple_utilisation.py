#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Exemple d'utilisation avancée du générateur d'affiches de félicitations
======================================================================

Ce script montre comment utiliser le générateur avec des paramètres personnalisés
et comment traiter plusieurs classes en une fois.
"""

import os
import pandas as pd
from congratulations_poster import CongratulationsPoster


def create_multiple_classes_data():
    """
    Crée des données pour plusieurs classes d'exemple.
    """
    classes_data = {
        '6ème A': {
            'students': [
                {'Nom': '<PERSON>', 'Note': 18.5, 'Classement': 1, 'Mention': 'Très Bien', 'Photo': 'photos/alice.jpg'},
                {'Nom': '<PERSON>', 'Note': 17.2, 'Classement': 2, 'Mention': 'Très Bien', 'Photo': 'photos/bob.jpg'},
                {'Nom': '<PERSON>', 'Note': 16.8, 'Classement': 3, 'Mention': 'Bien', 'Photo': 'photos/claire.jpg'},
                {'Nom': 'David Moreau', 'Note': 16.5, 'Classement': 4, 'Mention': 'Bien', 'Photo': 'photos/david.jpg'},
                {'Nom': 'Emma Bernard', 'Note': 15.9, 'Classement': 5, 'Mention': 'Bien', 'Photo': 'photos/emma.jpg'},
            ]
        },
        '5ème B': {
            'students': [
                {'Nom': 'Felix Dubois', 'Note': 19.0, 'Classement': 1, 'Mention': 'Très Bien', 'Photo': 'photos/felix.jpg'},
                {'Nom': 'Grace Leroy', 'Note': 18.2, 'Classement': 2, 'Mention': 'Très Bien', 'Photo': 'photos/grace.jpg'},
                {'Nom': 'Hugo Petit', 'Note': 17.8, 'Classement': 3, 'Mention': 'Très Bien', 'Photo': 'photos/hugo.jpg'},
                {'Nom': 'Iris Blanc', 'Note': 17.1, 'Classement': 4, 'Mention': 'Très Bien', 'Photo': 'photos/iris.jpg'},
                {'Nom': 'Jules Noir', 'Note': 16.9, 'Classement': 5, 'Mention': 'Bien', 'Photo': 'photos/jules.jpg'},
                {'Nom': 'Léa Rouge', 'Note': 16.3, 'Classement': 6, 'Mention': 'Bien', 'Photo': 'photos/lea.jpg'},
            ]
        },
        '4ème C': {
            'students': [
                {'Nom': 'Marc Vert', 'Note': 18.8, 'Classement': 1, 'Mention': 'Très Bien', 'Photo': 'photos/marc.jpg'},
                {'Nom': 'Nina Bleu', 'Note': 17.5, 'Classement': 2, 'Mention': 'Très Bien', 'Photo': 'photos/nina.jpg'},
                {'Nom': 'Oscar Jaune', 'Note': 16.2, 'Classement': 3, 'Mention': 'Bien', 'Photo': 'photos/oscar.jpg'},
            ]
        }
    }
    
    # Créer le dossier data s'il n'existe pas
    if not os.path.exists('data'):
        os.makedirs('data')
    
    # Sauvegarder chaque classe dans un fichier CSV séparé
    for class_name, class_info in classes_data.items():
        df = pd.DataFrame(class_info['students'])
        filename = f"data/{class_name.replace(' ', '_').lower()}.csv"
        df.to_csv(filename, index=False, encoding='utf-8')
        print(f"Fichier créé: {filename}")


def generate_custom_poster():
    """
    Exemple de génération d'affiche avec paramètres personnalisés.
    """
    print("\n=== Génération d'affiche personnalisée ===")
    
    # Créer un générateur avec des paramètres personnalisés
    generator = CongratulationsPoster(
        template_path='templates/fond_defaut.png',
        output_dir='output_custom'
    )
    
    # Personnaliser les couleurs
    generator.colors = {
        'text_primary': '#1A237E',      # Bleu foncé
        'text_secondary': '#424242',     # Gris foncé
        'gold': '#FFD700',              # Or
        'silver': '#C0C0C0',            # Argent
        'bronze': '#CD7F32'             # Bronze
    }
    
    # Personnaliser la mise en page
    generator.photo_size = 140
    generator.students_per_row = 3
    generator.margin = 80
    
    # Générer l'affiche
    csv_path = 'data/6ème_a.csv'
    if os.path.exists(csv_path):
        output_path = generator.generate_poster(csv_path, "6ème A - Trimestre 1")
        print(f"Affiche personnalisée générée: {output_path}")
    else:
        print(f"Fichier CSV non trouvé: {csv_path}")


def batch_generate_posters():
    """
    Génère des affiches pour plusieurs classes en lot.
    """
    print("\n=== Génération en lot ===")
    
    # Initialiser le générateur
    generator = CongratulationsPoster(
        template_path='templates/fond_defaut.png',
        output_dir='output_batch'
    )
    
    # Liste des classes à traiter
    classes = [
        ('data/6ème_a.csv', '6ème A'),
        ('data/5ème_b.csv', '5ème B'),
        ('data/4ème_c.csv', '4ème C')
    ]
    
    generated_files = []
    
    for csv_path, class_name in classes:
        if os.path.exists(csv_path):
            print(f"Génération pour {class_name}...")
            output_path = generator.generate_poster(csv_path, class_name)
            if output_path:
                generated_files.append(output_path)
                print(f"  ✅ {output_path}")
            else:
                print(f"  ❌ Erreur pour {class_name}")
        else:
            print(f"  ⚠️  Fichier non trouvé: {csv_path}")
    
    print(f"\n📊 Résumé: {len(generated_files)} affiches générées")
    return generated_files


def analyze_class_performance():
    """
    Analyse les performances des classes et affiche des statistiques.
    """
    print("\n=== Analyse des performances ===")
    
    csv_files = [
        ('data/6ème_a.csv', '6ème A'),
        ('data/5ème_b.csv', '5ème B'),
        ('data/4ème_c.csv', '4ème C')
    ]
    
    for csv_path, class_name in csv_files:
        if os.path.exists(csv_path):
            df = pd.read_csv(csv_path)
            
            print(f"\n📚 {class_name}:")
            print(f"   👥 Élèves: {len(df)}")
            print(f"   📊 Note moyenne: {df['Note'].mean():.2f}/20")
            print(f"   🏆 Meilleure note: {df['Note'].max()}/20")
            print(f"   🥇 Mentions 'Très Bien': {len(df[df['Mention'] == 'Très Bien'])}")
            print(f"   🥈 Mentions 'Bien': {len(df[df['Mention'] == 'Bien'])}")
            print(f"   🥉 Mentions 'Assez Bien': {len(df[df['Mention'] == 'Assez Bien'])}")


def main():
    """
    Fonction principale démontrant les différentes utilisations.
    """
    print("🎓 Exemple d'utilisation avancée du générateur d'affiches")
    print("=" * 60)
    
    # 1. Créer des données pour plusieurs classes
    print("1. Création des données d'exemple...")
    create_multiple_classes_data()
    
    # 2. Analyser les performances
    analyze_class_performance()
    
    # 3. Générer une affiche personnalisée
    generate_custom_poster()
    
    # 4. Génération en lot
    generated_files = batch_generate_posters()
    
    # 5. Résumé final
    print("\n" + "=" * 60)
    print("🎉 Traitement terminé!")
    print(f"📁 Fichiers générés dans les dossiers 'output_custom' et 'output_batch'")
    
    if generated_files:
        print("\n📋 Affiches générées:")
        for file_path in generated_files:
            print(f"   - {file_path}")


if __name__ == "__main__":
    main()
