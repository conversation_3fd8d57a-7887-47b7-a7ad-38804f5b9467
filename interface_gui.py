#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Interface Graphique Windows pour le Générateur d'Affiches de Félicitations
==========================================================================

Interface utilisateur avec aperçu avant export.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from tkinter import scrolledtext
import os
import pandas as pd
from PIL import Image, ImageTk
import threading
from congratulations_poster import CongratulationsPoster
from config import get_config


class InterfaceGenerateurAffiches:
    """Interface graphique principale."""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎓 Générateur d'Affiches de Félicitations")
        self.root.geometry("1400x900")
        self.root.state('zoomed')  # Maximiser la fenêtre

        # Thème de couleurs moderne
        self.colors = {
            'primary': '#2C3E50',      # Bleu foncé
            'secondary': '#3498DB',     # Bleu
            'accent': '#E74C3C',       # Rouge
            'success': '#27AE60',      # Vert
            'warning': '#F39C12',      # Orange
            'background': '#ECF0F1',   # Gris très clair
            'surface': '#FFFFFF',      # Blanc
            'text': '#2C3E50',         # Texte foncé
            'text_light': '#7F8C8D',   # Texte clair
            'border': '#BDC3C7'        # Bordure
        }

        self.root.configure(bg=self.colors['background'])

        # Variables principales
        self.csv_file = tk.StringVar()
        self.template_file = tk.StringVar(value="templates/fond_defaut.png")
        self.class_name = tk.StringVar(value="Ma Classe")
        self.output_dir = tk.StringVar(value="output")

        # Variables d'orientation et mise en page
        self.orientation = tk.StringVar(value="paysage")  # paysage ou portrait
        self.poster_width = tk.IntVar(value=1920)
        self.poster_height = tk.IntVar(value=1080)

        # Variables d'en-tête et pied de page
        self.header_text = tk.StringVar(value="🎓 TABLEAU D'HONNEUR 🎓")
        self.header_enabled = tk.BooleanVar(value=True)
        self.footer_text = tk.StringVar(value="Félicitations à tous nos élèves !")
        self.footer_enabled = tk.BooleanVar(value=True)
        self.school_name = tk.StringVar(value="École Exemple")
        self.academic_year = tk.StringVar(value="2024-2025")

        # Variables de style
        self.background_color = tk.StringVar(value="#F8F9FA")
        self.title_color = tk.StringVar(value="#2C3E50")
        self.border_style = tk.StringVar(value="moderne")

        # Générateur
        self.generator = None
        self.preview_image = None
        self.current_poster = None

        # Configuration
        self.config = get_config()

        self.setup_modern_style()
        self.setup_ui()
        self.load_default_files()

    def setup_modern_style(self):
        """Configure le style moderne de l'interface."""
        style = ttk.Style()
        style.theme_use('clam')

        # Configuration des styles personnalisés
        style.configure('Title.TLabel',
                       font=('Segoe UI', 16, 'bold'),
                       foreground=self.colors['primary'],
                       background=self.colors['background'])

        style.configure('Heading.TLabel',
                       font=('Segoe UI', 12, 'bold'),
                       foreground=self.colors['text'],
                       background=self.colors['surface'])

        style.configure('Modern.TFrame',
                       background=self.colors['surface'],
                       relief='flat',
                       borderwidth=1)

        style.configure('TLabelFrame',
                       background=self.colors['surface'],
                       relief='solid',
                       borderwidth=1)

        style.configure('TLabelFrame.Label',
                       font=('Segoe UI', 11, 'bold'),
                       foreground=self.colors['primary'],
                       background=self.colors['surface'])

        style.configure('Primary.TButton',
                       font=('Segoe UI', 10, 'bold'),
                       foreground='white')

        style.map('Primary.TButton',
                 background=[('active', self.colors['secondary']),
                           ('!active', self.colors['primary'])])

        style.configure('Success.TButton',
                       font=('Segoe UI', 10, 'bold'),
                       foreground='white')

        style.map('Success.TButton',
                 background=[('active', '#229954'),
                           ('!active', self.colors['success'])])

        style.configure('Warning.TButton',
                       font=('Segoe UI', 10, 'bold'),
                       foreground='white')

        style.map('Warning.TButton',
                 background=[('active', '#E67E22'),
                           ('!active', self.colors['warning'])])

    def setup_ui(self):
        """Configure l'interface utilisateur moderne."""
        # Barre de titre personnalisée
        self.setup_title_bar()

        # Conteneur principal avec padding
        main_container = ttk.Frame(self.root, style='Modern.TFrame', padding="15")
        main_container.pack(fill=tk.BOTH, expand=True)

        # Configuration du grid principal
        main_container.columnconfigure(0, weight=1, minsize=400)  # Config panel
        main_container.columnconfigure(1, weight=2, minsize=600)  # Preview panel
        main_container.rowconfigure(0, weight=1)

        # Panel de configuration (gauche)
        self.setup_enhanced_config_panel(main_container)

        # Panel d'aperçu (droite)
        self.setup_enhanced_preview_panel(main_container)

        # Barre d'état en bas
        self.setup_status_bar()

    def setup_title_bar(self):
        """Configure la barre de titre personnalisée."""
        title_frame = ttk.Frame(self.root, style='Modern.TFrame', padding="10")
        title_frame.pack(fill=tk.X)

        # Titre principal
        title_label = ttk.Label(
            title_frame,
            text="🎓 Générateur d'Affiches de Félicitations",
            style='Title.TLabel'
        )
        title_label.pack(side=tk.LEFT)

        # Boutons de la barre de titre
        title_buttons = ttk.Frame(title_frame)
        title_buttons.pack(side=tk.RIGHT)

        ttk.Button(title_buttons, text="❓ Aide",
                  command=self.show_help).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(title_buttons, text="⚙️ Paramètres",
                  command=self.open_config).pack(side=tk.RIGHT, padx=(5, 0))

        # Séparateur
        separator = ttk.Separator(self.root, orient='horizontal')
        separator.pack(fill=tk.X, pady=(0, 5))

    def setup_enhanced_config_panel(self, parent):
        """Configure le panel de configuration amélioré."""
        # Conteneur principal avec scrollbar
        config_container = ttk.Frame(parent, style='Modern.TFrame')
        config_container.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))

        # Canvas avec scrollbar pour le contenu
        canvas = tk.Canvas(config_container, bg=self.colors['surface'], highlightthickness=0)
        scrollbar = ttk.Scrollbar(config_container, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas, style='Modern.TFrame')

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Sections de configuration
        self.setup_file_section(scrollable_frame)
        self.setup_layout_section(scrollable_frame)
        self.setup_content_section(scrollable_frame)
        self.setup_style_section(scrollable_frame)
        self.setup_info_section(scrollable_frame)

        # Bind mousewheel to canvas
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)

    def setup_file_section(self, parent):
        """Section de sélection des fichiers."""
        file_frame = ttk.LabelFrame(parent, text="📁 Fichiers", padding="15")
        file_frame.pack(fill=tk.X, pady=(0, 10))

        # Fichier CSV
        ttk.Label(file_frame, text="📊 Données CSV:", style='Heading.TLabel').pack(anchor=tk.W, pady=(0, 5))
        csv_frame = ttk.Frame(file_frame)
        csv_frame.pack(fill=tk.X, pady=(0, 10))

        self.csv_entry = ttk.Entry(csv_frame, textvariable=self.csv_file, font=('Segoe UI', 9))
        self.csv_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(csv_frame, text="📂", command=self.browse_csv, width=3).pack(side=tk.RIGHT, padx=(5, 0))

        # Template
        ttk.Label(file_frame, text="🎨 Template:", style='Heading.TLabel').pack(anchor=tk.W, pady=(0, 5))
        template_frame = ttk.Frame(file_frame)
        template_frame.pack(fill=tk.X, pady=(0, 10))

        self.template_entry = ttk.Entry(template_frame, textvariable=self.template_file, font=('Segoe UI', 9))
        self.template_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(template_frame, text="📂", command=self.browse_template, width=3).pack(side=tk.RIGHT, padx=(5, 0))

        # Dossier de sortie
        ttk.Label(file_frame, text="💾 Sortie:", style='Heading.TLabel').pack(anchor=tk.W, pady=(0, 5))
        output_frame = ttk.Frame(file_frame)
        output_frame.pack(fill=tk.X)

        self.output_entry = ttk.Entry(output_frame, textvariable=self.output_dir, font=('Segoe UI', 9))
        self.output_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(output_frame, text="📂", command=self.browse_output, width=3).pack(side=tk.RIGHT, padx=(5, 0))

    def setup_layout_section(self, parent):
        """Section de mise en page et orientation."""
        layout_frame = ttk.LabelFrame(parent, text="📐 Mise en Page", padding="15")
        layout_frame.pack(fill=tk.X, pady=(0, 10))

        # Orientation
        ttk.Label(layout_frame, text="🔄 Orientation:", style='Heading.TLabel').pack(anchor=tk.W, pady=(0, 5))
        orientation_frame = ttk.Frame(layout_frame)
        orientation_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Radiobutton(orientation_frame, text="📱 Portrait", variable=self.orientation,
                       value="portrait", command=self.on_orientation_change).pack(side=tk.LEFT, padx=(0, 20))
        ttk.Radiobutton(orientation_frame, text="🖥️ Paysage", variable=self.orientation,
                       value="paysage", command=self.on_orientation_change).pack(side=tk.LEFT)

        # Dimensions personnalisées
        ttk.Label(layout_frame, text="📏 Dimensions:", style='Heading.TLabel').pack(anchor=tk.W, pady=(10, 5))
        dimensions_frame = ttk.Frame(layout_frame)
        dimensions_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(dimensions_frame, text="L:").pack(side=tk.LEFT)
        width_entry = ttk.Entry(dimensions_frame, textvariable=self.poster_width, width=6)
        width_entry.pack(side=tk.LEFT, padx=(5, 10))

        ttk.Label(dimensions_frame, text="H:").pack(side=tk.LEFT)
        height_entry = ttk.Entry(dimensions_frame, textvariable=self.poster_height, width=6)
        height_entry.pack(side=tk.LEFT, padx=(5, 10))

        # Presets rapides
        presets_frame = ttk.Frame(layout_frame)
        presets_frame.pack(fill=tk.X)

        ttk.Button(presets_frame, text="HD", command=lambda: self.apply_preset("hd"), width=8).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(presets_frame, text="4K", command=lambda: self.apply_preset("4k"), width=8).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(presets_frame, text="A4", command=lambda: self.apply_preset("a4"), width=8).pack(side=tk.LEFT)

    def setup_content_section(self, parent):
        """Section de contenu (en-tête, pied de page)."""
        content_frame = ttk.LabelFrame(parent, text="📝 Contenu", padding="15")
        content_frame.pack(fill=tk.X, pady=(0, 10))

        # Nom de classe
        ttk.Label(content_frame, text="🏫 Classe:", style='Heading.TLabel').pack(anchor=tk.W, pady=(0, 5))
        ttk.Entry(content_frame, textvariable=self.class_name, font=('Segoe UI', 10, 'bold')).pack(fill=tk.X, pady=(0, 10))

        # En-tête
        header_frame = ttk.Frame(content_frame)
        header_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Checkbutton(header_frame, text="📋 En-tête personnalisé",
                       variable=self.header_enabled).pack(anchor=tk.W)
        ttk.Entry(content_frame, textvariable=self.header_text,
                 font=('Segoe UI', 9), state='normal' if self.header_enabled.get() else 'disabled').pack(fill=tk.X, pady=(5, 10))

        # École et année
        school_frame = ttk.Frame(content_frame)
        school_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(school_frame, text="🏛️ École:").pack(side=tk.LEFT)
        ttk.Entry(school_frame, textvariable=self.school_name, width=15).pack(side=tk.LEFT, padx=(5, 10), fill=tk.X, expand=True)

        ttk.Label(school_frame, text="📅 Année:").pack(side=tk.LEFT)
        ttk.Entry(school_frame, textvariable=self.academic_year, width=10).pack(side=tk.LEFT, padx=(5, 0))

        # Pied de page
        footer_frame = ttk.Frame(content_frame)
        footer_frame.pack(fill=tk.X)

        ttk.Checkbutton(footer_frame, text="📄 Pied de page",
                       variable=self.footer_enabled).pack(anchor=tk.W)
        ttk.Entry(content_frame, textvariable=self.footer_text,
                 font=('Segoe UI', 9)).pack(fill=tk.X, pady=(5, 0))

    def setup_style_section(self, parent):
        """Section de style et couleurs."""
        style_frame = ttk.LabelFrame(parent, text="🎨 Style", padding="15")
        style_frame.pack(fill=tk.X, pady=(0, 10))

        # Couleur de fond
        color_frame = ttk.Frame(style_frame)
        color_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(color_frame, text="🎨 Fond:").pack(side=tk.LEFT)
        ttk.Entry(color_frame, textvariable=self.background_color, width=10).pack(side=tk.LEFT, padx=(5, 10))

        ttk.Label(color_frame, text="📝 Titre:").pack(side=tk.LEFT)
        ttk.Entry(color_frame, textvariable=self.title_color, width=10).pack(side=tk.LEFT, padx=(5, 0))

        # Style de bordure
        ttk.Label(style_frame, text="🖼️ Bordure:", style='Heading.TLabel').pack(anchor=tk.W, pady=(0, 5))
        border_frame = ttk.Frame(style_frame)
        border_frame.pack(fill=tk.X)

        ttk.Radiobutton(border_frame, text="Moderne", variable=self.border_style, value="moderne").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(border_frame, text="Classique", variable=self.border_style, value="classique").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(border_frame, text="Aucune", variable=self.border_style, value="aucune").pack(side=tk.LEFT)

    def setup_info_section(self, parent):
        """Section d'informations CSV."""
        info_frame = ttk.LabelFrame(parent, text="📊 Informations", padding="15")
        info_frame.pack(fill=tk.X, pady=(0, 10))

        # Zone de texte pour les informations
        self.info_text = tk.Text(info_frame, height=12, width=40,
                                font=('Consolas', 9),
                                bg=self.colors['surface'],
                                fg=self.colors['text'],
                                relief='flat',
                                wrap=tk.WORD)
        self.info_text.pack(fill=tk.BOTH, expand=True)

        # Boutons d'action rapide
        action_frame = ttk.Frame(info_frame)
        action_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(action_frame, text="📊 Analyser CSV",
                  command=self.load_csv_info, style='Primary.TButton').pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(action_frame, text="🎨 Aperçu",
                  command=self.generate_preview, style='Success.TButton').pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(action_frame, text="💾 Exporter",
                  command=self.export_image, style='Warning.TButton').pack(side=tk.LEFT)

    def setup_enhanced_preview_panel(self, parent):
        """Configure le panel d'aperçu amélioré."""
        preview_container = ttk.LabelFrame(parent, text="👁️ Aperçu en Temps Réel",
                                         padding="15")
        preview_container.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Barre d'outils de l'aperçu
        toolbar = ttk.Frame(preview_container)
        toolbar.pack(fill=tk.X, pady=(0, 10))

        # Boutons de zoom
        zoom_frame = ttk.Frame(toolbar)
        zoom_frame.pack(side=tk.LEFT)

        ttk.Button(zoom_frame, text="🔍+", command=self.zoom_in, width=4).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Button(zoom_frame, text="🔍-", command=self.zoom_out, width=4).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Button(zoom_frame, text="⚡", command=self.zoom_fit, width=4).pack(side=tk.LEFT, padx=(0, 10))

        # Informations de l'aperçu
        self.preview_info = ttk.Label(toolbar, text="Aucun aperçu", foreground=self.colors['text_light'])
        self.preview_info.pack(side=tk.LEFT, padx=(10, 0))

        # Bouton de génération rapide
        ttk.Button(toolbar, text="🔄 Régénérer",
                  command=self.generate_preview, style='Primary.TButton').pack(side=tk.RIGHT)

        # Canvas d'aperçu avec scrollbars
        canvas_frame = ttk.Frame(preview_container)
        canvas_frame.pack(fill=tk.BOTH, expand=True)

        self.preview_canvas = tk.Canvas(canvas_frame, bg='white', relief='sunken', bd=2)

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=self.preview_canvas.yview)
        h_scrollbar = ttk.Scrollbar(canvas_frame, orient="horizontal", command=self.preview_canvas.xview)

        self.preview_canvas.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Grid layout pour canvas et scrollbars
        self.preview_canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))

        canvas_frame.columnconfigure(0, weight=1)
        canvas_frame.rowconfigure(0, weight=1)

        # Variables de zoom
        self.zoom_factor = 1.0
        self.original_image = None

    def setup_status_bar(self):
        """Configure la barre d'état."""
        status_frame = ttk.Frame(self.root, padding="5")
        status_frame.pack(fill=tk.X, side=tk.BOTTOM)

        # Séparateur
        separator = ttk.Separator(self.root, orient='horizontal')
        separator.pack(fill=tk.X, side=tk.BOTTOM)

        # Informations de statut
        self.status_label = ttk.Label(status_frame, text="Prêt", foreground=self.colors['text_light'])
        self.status_label.pack(side=tk.LEFT)

        # Barre de progression
        self.progress = ttk.Progressbar(status_frame, mode='indeterminate', length=200)
        self.progress.pack(side=tk.RIGHT, padx=(10, 0))

    def on_orientation_change(self):
        """Gère le changement d'orientation."""
        if self.orientation.get() == "portrait":
            # Échanger largeur et hauteur pour le portrait
            current_width = self.poster_width.get()
            current_height = self.poster_height.get()
            if current_width > current_height:
                self.poster_width.set(current_height)
                self.poster_height.set(current_width)
        else:  # paysage
            # S'assurer que la largeur est supérieure à la hauteur
            current_width = self.poster_width.get()
            current_height = self.poster_height.get()
            if current_height > current_width:
                self.poster_width.set(current_height)
                self.poster_height.set(current_width)

        self.update_status("Orientation changée")

    def apply_preset(self, preset_name):
        """Applique un preset de dimensions."""
        presets = {
            "hd": {"width": 1920, "height": 1080},
            "4k": {"width": 3840, "height": 2160},
            "a4": {"width": 2480, "height": 3508}
        }

        if preset_name in presets:
            preset = presets[preset_name]
            if self.orientation.get() == "portrait":
                self.poster_width.set(min(preset["width"], preset["height"]))
                self.poster_height.set(max(preset["width"], preset["height"]))
            else:
                self.poster_width.set(max(preset["width"], preset["height"]))
                self.poster_height.set(min(preset["width"], preset["height"]))

            self.update_status(f"Preset {preset_name.upper()} appliqué")

    def update_status(self, message):
        """Met à jour la barre d'état."""
        self.status_label.config(text=message)
        self.root.after(3000, lambda: self.status_label.config(text="Prêt"))

    def show_help(self):
        """Affiche l'aide."""
        help_text = """
🎓 AIDE - Générateur d'Affiches de Félicitations

📋 ÉTAPES RAPIDES:
1. Sélectionnez votre fichier CSV
2. Configurez l'orientation et le contenu
3. Cliquez sur "Aperçu" pour voir le résultat
4. Exportez votre affiche

📊 FORMAT CSV REQUIS:
- Nom, Note, Classement, Mention, Photo

🎨 PERSONNALISATION:
- Orientation: Portrait ou Paysage
- En-tête et pied de page personnalisables
- Couleurs et styles configurables

💡 CONSEILS:
- Utilisez des photos 200x200px minimum
- Testez avec peu d'élèves d'abord
- Les photos manquantes sont remplacées automatiquement
        """

        messagebox.showinfo("Aide", help_text)

    def zoom_in(self):
        """Zoom avant sur l'aperçu."""
        self.zoom_factor *= 1.2
        self.update_preview_zoom()

    def zoom_out(self):
        """Zoom arrière sur l'aperçu."""
        self.zoom_factor /= 1.2
        self.update_preview_zoom()

    def zoom_fit(self):
        """Ajuste le zoom pour que l'aperçu s'adapte à la fenêtre."""
        if self.original_image:
            canvas_width = self.preview_canvas.winfo_width()
            canvas_height = self.preview_canvas.winfo_height()
            img_width, img_height = self.original_image.size

            zoom_x = canvas_width / img_width
            zoom_y = canvas_height / img_height
            self.zoom_factor = min(zoom_x, zoom_y) * 0.9  # 90% pour laisser une marge

            self.update_preview_zoom()

    def update_preview_zoom(self):
        """Met à jour l'affichage avec le nouveau zoom."""
        if self.original_image:
            new_size = (
                int(self.original_image.size[0] * self.zoom_factor),
                int(self.original_image.size[1] * self.zoom_factor)
            )

            zoomed_image = self.original_image.resize(new_size, Image.Resampling.LANCZOS)
            self.preview_image = ImageTk.PhotoImage(zoomed_image)

            self.preview_canvas.delete("all")
            self.preview_canvas.create_image(
                new_size[0]//2, new_size[1]//2,
                image=self.preview_image,
                anchor=tk.CENTER
            )

            self.preview_canvas.configure(scrollregion=self.preview_canvas.bbox("all"))

            # Mettre à jour les informations
            zoom_percent = int(self.zoom_factor * 100)
            self.preview_info.config(text=f"Zoom: {zoom_percent}% | {new_size[0]}×{new_size[1]}")

    def setup_config_frame(self, parent):
        """Configure le frame de configuration."""
        config_frame = ttk.LabelFrame(parent, text="📋 Configuration", padding="10")
        config_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))

        # Fichier CSV
        ttk.Label(config_frame, text="📊 Fichier CSV:").grid(row=0, column=0, sticky=tk.W, pady=5)
        csv_frame = ttk.Frame(config_frame)
        csv_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        csv_frame.columnconfigure(0, weight=1)

        ttk.Entry(csv_frame, textvariable=self.csv_file, width=40).grid(row=0, column=0, sticky=(tk.W, tk.E))
        ttk.Button(csv_frame, text="Parcourir", command=self.browse_csv).grid(row=0, column=1, padx=(5, 0))

        # Template
        ttk.Label(config_frame, text="🎨 Template:").grid(row=2, column=0, sticky=tk.W, pady=5)
        template_frame = ttk.Frame(config_frame)
        template_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        template_frame.columnconfigure(0, weight=1)

        ttk.Entry(template_frame, textvariable=self.template_file, width=40).grid(row=0, column=0, sticky=(tk.W, tk.E))
        ttk.Button(template_frame, text="Parcourir", command=self.browse_template).grid(row=0, column=1, padx=(5, 0))

        # Nom de classe
        ttk.Label(config_frame, text="📚 Nom de la classe:").grid(row=4, column=0, sticky=tk.W, pady=5)
        ttk.Entry(config_frame, textvariable=self.class_name, width=40).grid(row=5, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # Dossier de sortie
        ttk.Label(config_frame, text="📁 Dossier de sortie:").grid(row=6, column=0, sticky=tk.W, pady=5)
        output_frame = ttk.Frame(config_frame)
        output_frame.grid(row=7, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        output_frame.columnconfigure(0, weight=1)

        ttk.Entry(output_frame, textvariable=self.output_dir, width=40).grid(row=0, column=0, sticky=(tk.W, tk.E))
        ttk.Button(output_frame, text="Parcourir", command=self.browse_output).grid(row=0, column=1, padx=(5, 0))

        # Informations CSV
        ttk.Label(config_frame, text="📈 Informations CSV:").grid(row=8, column=0, sticky=tk.W, pady=(20, 5))
        self.info_text = scrolledtext.ScrolledText(config_frame, height=8, width=50)
        self.info_text.grid(row=9, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        config_frame.rowconfigure(9, weight=1)

    def setup_preview_frame(self, parent):
        """Configure le frame d'aperçu."""
        preview_frame = ttk.LabelFrame(parent, text="👁️ Aperçu", padding="10")
        preview_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        preview_frame.columnconfigure(0, weight=1)
        preview_frame.rowconfigure(1, weight=1)

        # Bouton de génération d'aperçu
        ttk.Button(preview_frame, text="🔄 Générer Aperçu",
                  command=self.generate_preview).grid(row=0, column=0, pady=(0, 10))

        # Canvas pour l'aperçu
        self.preview_canvas = tk.Canvas(preview_frame, bg='white', relief='sunken', bd=2)
        self.preview_canvas.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Scrollbars pour le canvas
        v_scrollbar = ttk.Scrollbar(preview_frame, orient="vertical", command=self.preview_canvas.yview)
        v_scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))
        self.preview_canvas.configure(yscrollcommand=v_scrollbar.set)

        h_scrollbar = ttk.Scrollbar(preview_frame, orient="horizontal", command=self.preview_canvas.xview)
        h_scrollbar.grid(row=2, column=0, sticky=(tk.W, tk.E))
        self.preview_canvas.configure(xscrollcommand=h_scrollbar.set)

    def setup_controls_frame(self, parent):
        """Configure le frame de contrôles."""
        controls_frame = ttk.Frame(parent)
        controls_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(20, 0))

        # Barre de progression
        self.progress = ttk.Progressbar(controls_frame, mode='indeterminate')
        self.progress.grid(row=0, column=0, columnspan=4, sticky=(tk.W, tk.E), pady=(0, 10))

        # Boutons
        ttk.Button(controls_frame, text="📊 Charger CSV",
                  command=self.load_csv_info).grid(row=1, column=0, padx=(0, 10))

        ttk.Button(controls_frame, text="🎨 Générer Aperçu",
                  command=self.generate_preview).grid(row=1, column=1, padx=(0, 10))

        ttk.Button(controls_frame, text="💾 Exporter Image",
                  command=self.export_image).grid(row=1, column=2, padx=(0, 10))

        ttk.Button(controls_frame, text="⚙️ Configuration",
                  command=self.open_config).grid(row=1, column=3)

        controls_frame.columnconfigure(0, weight=1)

    def browse_csv(self):
        """Ouvre le dialogue de sélection de fichier CSV."""
        filename = filedialog.askopenfilename(
            title="Sélectionner un fichier CSV",
            filetypes=[("Fichiers CSV", "*.csv"), ("Tous les fichiers", "*.*")],
            initialdir="data"
        )
        if filename:
            self.csv_file.set(filename)
            self.load_csv_info()

    def browse_template(self):
        """Ouvre le dialogue de sélection de template."""
        filename = filedialog.askopenfilename(
            title="Sélectionner un template",
            filetypes=[("Images", "*.png *.jpg *.jpeg *.bmp"), ("Tous les fichiers", "*.*")],
            initialdir="templates"
        )
        if filename:
            self.template_file.set(filename)

    def browse_output(self):
        """Ouvre le dialogue de sélection de dossier de sortie."""
        dirname = filedialog.askdirectory(
            title="Sélectionner le dossier de sortie",
            initialdir="output"
        )
        if dirname:
            self.output_dir.set(dirname)

    def load_default_files(self):
        """Charge les fichiers par défaut."""
        # Chercher un fichier CSV par défaut
        csv_files = []
        if os.path.exists('data'):
            csv_files = [f for f in os.listdir('data') if f.endswith('.csv')]

        if csv_files:
            default_csv = os.path.join('data', csv_files[0])
            self.csv_file.set(default_csv)
            self.load_csv_info()

    def load_csv_info(self):
        """Charge et affiche les informations du fichier CSV."""
        csv_path = self.csv_file.get()
        if not csv_path or not os.path.exists(csv_path):
            self.info_text.delete(1.0, tk.END)
            self.info_text.insert(tk.END, "❌ Fichier CSV non trouvé")
            return

        try:
            df = pd.read_csv(csv_path)

            # Vérifier les colonnes requises
            required_cols = ['Nom', 'Note', 'Classement', 'Mention', 'Photo']
            missing_cols = [col for col in required_cols if col not in df.columns]

            info = f"📊 INFORMATIONS DU FICHIER CSV\n"
            info += f"{'='*40}\n\n"
            info += f"📁 Fichier: {os.path.basename(csv_path)}\n"
            info += f"👥 Nombre d'élèves: {len(df)}\n\n"

            if missing_cols:
                info += f"❌ Colonnes manquantes: {', '.join(missing_cols)}\n\n"
            else:
                info += f"✅ Format valide\n\n"

                # Statistiques
                info += f"📈 STATISTIQUES\n"
                info += f"{'-'*20}\n"
                info += f"📊 Note moyenne: {df['Note'].mean():.2f}/20\n"
                info += f"🏆 Meilleure note: {df['Note'].max()}/20\n"
                info += f"📉 Note la plus basse: {df['Note'].min()}/20\n\n"

                # Répartition des mentions
                mentions = df['Mention'].value_counts()
                info += f"🏅 RÉPARTITION DES MENTIONS\n"
                info += f"{'-'*30}\n"
                for mention, count in mentions.items():
                    info += f"   {mention}: {count} élève(s)\n"

                info += f"\n📋 APERÇU DES DONNÉES\n"
                info += f"{'-'*25}\n"
                for i, row in df.head(5).iterrows():
                    info += f"{row['Classement']:2d}. {row['Nom']} - {row['Note']}/20 ({row['Mention']})\n"

                if len(df) > 5:
                    info += f"... et {len(df) - 5} autres élèves\n"

            self.info_text.delete(1.0, tk.END)
            self.info_text.insert(tk.END, info)

        except Exception as e:
            self.info_text.delete(1.0, tk.END)
            self.info_text.insert(tk.END, f"❌ Erreur lors du chargement:\n{str(e)}")

    def generate_preview(self):
        """Génère l'aperçu de l'affiche."""
        if not self.validate_inputs():
            return

        # Démarrer la barre de progression
        self.progress.start()

        # Générer l'aperçu dans un thread séparé
        thread = threading.Thread(target=self._generate_preview_thread)
        thread.daemon = True
        thread.start()

    def _generate_preview_thread(self):
        """Génère l'aperçu dans un thread séparé."""
        try:
            # Créer le générateur
            self.generator = CongratulationsPoster(
                template_path=self.template_file.get(),
                output_dir=self.output_dir.get()
            )

            # Générer l'affiche temporaire
            temp_output = os.path.join(self.output_dir.get(), "temp_preview.png")

            # Charger les données
            df = self.generator.load_students_data(self.csv_file.get())
            if df.empty:
                raise Exception("Impossible de charger les données CSV")

            # Créer l'affiche
            if os.path.exists(self.template_file.get()):
                poster = Image.open(self.template_file.get()).convert('RGBA')
                poster = poster.resize((self.generator.poster_width, self.generator.poster_height), Image.Resampling.LANCZOS)
            else:
                poster = Image.new('RGBA', (self.generator.poster_width, self.generator.poster_height), (240, 248, 255, 255))

            from PIL import ImageDraw
            draw = ImageDraw.Draw(poster)

            # Ajouter le titre
            self.generator.add_title(draw, self.class_name.get())

            # Calculer les positions et ajouter les élèves
            positions = self.generator.calculate_positions(len(df))
            for idx, (_, student) in enumerate(df.iterrows()):
                if idx < len(positions):
                    x, y = positions[idx]
                    rank = student['Classement']
                    self.generator.add_student_to_poster(poster, draw, student, x, y, rank)

            # Sauvegarder temporairement
            poster.save(temp_output, 'PNG')
            self.current_poster = poster

            # Mettre à jour l'aperçu dans le thread principal
            self.root.after(0, self._update_preview, temp_output)

        except Exception as e:
            self.root.after(0, self._show_error, f"Erreur lors de la génération: {str(e)}")
        finally:
            self.root.after(0, self.progress.stop)

    def _update_preview(self, image_path):
        """Met à jour l'aperçu dans l'interface."""
        try:
            # Charger l'image
            image = Image.open(image_path)

            # Redimensionner pour l'aperçu (max 600x400)
            image.thumbnail((600, 400), Image.Resampling.LANCZOS)

            # Convertir pour tkinter
            self.preview_image = ImageTk.PhotoImage(image)

            # Effacer le canvas
            self.preview_canvas.delete("all")

            # Afficher l'image
            self.preview_canvas.create_image(
                self.preview_canvas.winfo_width()//2,
                self.preview_canvas.winfo_height()//2,
                image=self.preview_image,
                anchor=tk.CENTER
            )

            # Configurer la zone de défilement
            self.preview_canvas.configure(scrollregion=self.preview_canvas.bbox("all"))

            # Supprimer le fichier temporaire
            if os.path.exists(image_path):
                os.remove(image_path)

        except Exception as e:
            self._show_error(f"Erreur lors de l'affichage: {str(e)}")

    def _show_error(self, message):
        """Affiche un message d'erreur."""
        messagebox.showerror("Erreur", message)

    def validate_inputs(self):
        """Valide les entrées utilisateur."""
        if not self.csv_file.get():
            messagebox.showerror("Erreur", "Veuillez sélectionner un fichier CSV")
            return False

        if not os.path.exists(self.csv_file.get()):
            messagebox.showerror("Erreur", "Le fichier CSV n'existe pas")
            return False

        if not self.class_name.get().strip():
            messagebox.showerror("Erreur", "Veuillez entrer un nom de classe")
            return False

        # Créer le dossier de sortie s'il n'existe pas
        output_dir = self.output_dir.get()
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        return True

    def export_image(self):
        """Exporte l'affiche finale."""
        if not self.current_poster:
            messagebox.showwarning("Attention", "Veuillez d'abord générer un aperçu")
            return

        if not self.validate_inputs():
            return

        # Demander le nom du fichier
        filename = filedialog.asksaveasfilename(
            title="Exporter l'affiche",
            defaultextension=".png",
            filetypes=[
                ("PNG", "*.png"),
                ("JPEG", "*.jpg"),
                ("BMP", "*.bmp"),
                ("Tous les fichiers", "*.*")
            ],
            initialdir=self.output_dir.get(),
            initialname=f"affiche_{self.class_name.get().replace(' ', '_')}.png"
        )

        if filename:
            try:
                # Sauvegarder l'affiche
                self.current_poster.save(filename)

                # Afficher les informations
                file_size = os.path.getsize(filename) / 1024  # KB
                messagebox.showinfo(
                    "Succès",
                    f"✅ Affiche exportée avec succès!\n\n"
                    f"📁 Fichier: {filename}\n"
                    f"💾 Taille: {file_size:.1f} KB\n"
                    f"📐 Dimensions: {self.current_poster.size[0]}x{self.current_poster.size[1]}"
                )

            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de l'export: {str(e)}")

    def open_config(self):
        """Ouvre la fenêtre de configuration."""
        ConfigWindow(self.root, self.config)

    def run(self):
        """Lance l'interface."""
        self.root.mainloop()


class ConfigWindow:
    """Fenêtre de configuration avancée."""

    def __init__(self, parent, config):
        self.parent = parent
        self.config = config

        # Créer la fenêtre
        self.window = tk.Toplevel(parent)
        self.window.title("⚙️ Configuration Avancée")
        self.window.geometry("600x500")
        self.window.configure(bg='#f0f0f0')
        self.window.transient(parent)
        self.window.grab_set()

        # Variables
        self.poster_width = tk.IntVar(value=config['poster']['width'])
        self.poster_height = tk.IntVar(value=config['poster']['height'])
        self.photo_size = tk.IntVar(value=config['poster']['photo_size'])
        self.students_per_row = tk.IntVar(value=config['poster']['students_per_row'])
        self.margin = tk.IntVar(value=config['poster']['margin'])

        self.setup_config_ui()

    def setup_config_ui(self):
        """Configure l'interface de configuration."""
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Titre
        ttk.Label(main_frame, text="⚙️ Configuration Avancée",
                 font=('Arial', 14, 'bold')).pack(pady=(0, 20))

        # Notebook pour les onglets
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # Onglet Dimensions
        self.setup_dimensions_tab(notebook)

        # Onglet Couleurs
        self.setup_colors_tab(notebook)

        # Onglet Polices
        self.setup_fonts_tab(notebook)

        # Boutons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        ttk.Button(button_frame, text="💾 Sauvegarder",
                  command=self.save_config).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(button_frame, text="🔄 Réinitialiser",
                  command=self.reset_config).pack(side=tk.RIGHT)
        ttk.Button(button_frame, text="❌ Annuler",
                  command=self.window.destroy).pack(side=tk.RIGHT, padx=(0, 10))

    def setup_dimensions_tab(self, notebook):
        """Configure l'onglet des dimensions."""
        frame = ttk.Frame(notebook, padding="10")
        notebook.add(frame, text="📐 Dimensions")

        # Dimensions de l'affiche
        ttk.Label(frame, text="📏 Dimensions de l'affiche:",
                 font=('Arial', 10, 'bold')).grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))

        ttk.Label(frame, text="Largeur (px):").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(frame, textvariable=self.poster_width, width=10).grid(row=1, column=1, sticky=tk.W, padx=(10, 0))

        ttk.Label(frame, text="Hauteur (px):").grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Entry(frame, textvariable=self.poster_height, width=10).grid(row=2, column=1, sticky=tk.W, padx=(10, 0))

        # Mise en page
        ttk.Label(frame, text="🎯 Mise en page:",
                 font=('Arial', 10, 'bold')).grid(row=3, column=0, columnspan=2, sticky=tk.W, pady=(20, 10))

        ttk.Label(frame, text="Taille des photos (px):").grid(row=4, column=0, sticky=tk.W, pady=5)
        ttk.Entry(frame, textvariable=self.photo_size, width=10).grid(row=4, column=1, sticky=tk.W, padx=(10, 0))

        ttk.Label(frame, text="Élèves par ligne:").grid(row=5, column=0, sticky=tk.W, pady=5)
        ttk.Entry(frame, textvariable=self.students_per_row, width=10).grid(row=5, column=1, sticky=tk.W, padx=(10, 0))

        ttk.Label(frame, text="Marge (px):").grid(row=6, column=0, sticky=tk.W, pady=5)
        ttk.Entry(frame, textvariable=self.margin, width=10).grid(row=6, column=1, sticky=tk.W, padx=(10, 0))

        # Presets
        ttk.Label(frame, text="📋 Presets:",
                 font=('Arial', 10, 'bold')).grid(row=7, column=0, columnspan=2, sticky=tk.W, pady=(20, 10))

        preset_frame = ttk.Frame(frame)
        preset_frame.grid(row=8, column=0, columnspan=2, sticky=tk.W)

        ttk.Button(preset_frame, text="HD (1920x1080)",
                  command=lambda: self.apply_preset("hd")).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(preset_frame, text="4K (3840x2160)",
                  command=lambda: self.apply_preset("4k")).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(preset_frame, text="A4 (2480x3508)",
                  command=lambda: self.apply_preset("a4")).pack(side=tk.LEFT)

    def setup_colors_tab(self, notebook):
        """Configure l'onglet des couleurs."""
        frame = ttk.Frame(notebook, padding="10")
        notebook.add(frame, text="🎨 Couleurs")

        ttk.Label(frame, text="🎨 Configuration des couleurs",
                 font=('Arial', 12, 'bold')).pack(pady=(0, 20))

        # Couleurs des mentions
        mentions_frame = ttk.LabelFrame(frame, text="🏅 Couleurs des mentions", padding="10")
        mentions_frame.pack(fill=tk.X, pady=(0, 20))

        colors = [
            ("🥇 Très Bien", self.config['colors']['gold']),
            ("🥈 Bien", self.config['colors']['silver']),
            ("🥉 Assez Bien", self.config['colors']['bronze']),
            ("📝 Passable", self.config['colors']['text_secondary'])
        ]

        for i, (label, color) in enumerate(colors):
            color_frame = ttk.Frame(mentions_frame)
            color_frame.pack(fill=tk.X, pady=2)

            ttk.Label(color_frame, text=label, width=15).pack(side=tk.LEFT)
            color_label = tk.Label(color_frame, text="   ", bg=color, relief='solid', bd=1)
            color_label.pack(side=tk.LEFT, padx=(10, 5))
            ttk.Label(color_frame, text=color).pack(side=tk.LEFT)

        # Note d'information
        info_text = ("💡 Les couleurs peuvent être modifiées dans le fichier config.py\n"
                    "Utilisez des codes hexadécimaux (ex: #FF0000 pour rouge)")
        ttk.Label(frame, text=info_text, foreground='gray').pack(pady=20)

    def setup_fonts_tab(self, notebook):
        """Configure l'onglet des polices."""
        frame = ttk.Frame(notebook, padding="10")
        notebook.add(frame, text="🔤 Polices")

        ttk.Label(frame, text="🔤 Configuration des polices",
                 font=('Arial', 12, 'bold')).pack(pady=(0, 20))

        # Tailles de police
        sizes_frame = ttk.LabelFrame(frame, text="📏 Tailles de police", padding="10")
        sizes_frame.pack(fill=tk.X, pady=(0, 20))

        font_sizes = [
            ("Titre", self.config['fonts']['title_size']),
            ("Noms d'élèves", self.config['fonts']['name_size']),
            ("Informations", self.config['fonts']['info_size'])
        ]

        for label, size in font_sizes:
            size_frame = ttk.Frame(sizes_frame)
            size_frame.pack(fill=tk.X, pady=2)

            ttk.Label(size_frame, text=f"{label}:", width=15).pack(side=tk.LEFT)
            ttk.Label(size_frame, text=f"{size}px").pack(side=tk.LEFT, padx=(10, 0))

        # Note d'information
        info_text = ("💡 Les polices peuvent être modifiées dans le fichier config.py\n"
                    "Police par défaut: Arial (système)")
        ttk.Label(frame, text=info_text, foreground='gray').pack(pady=20)

    def apply_preset(self, preset_name):
        """Applique un preset de dimensions."""
        presets = {
            "hd": {"width": 1920, "height": 1080, "photo_size": 120, "students_per_row": 4, "margin": 50},
            "4k": {"width": 3840, "height": 2160, "photo_size": 240, "students_per_row": 6, "margin": 100},
            "a4": {"width": 2480, "height": 3508, "photo_size": 150, "students_per_row": 3, "margin": 200}
        }

        if preset_name in presets:
            preset = presets[preset_name]
            self.poster_width.set(preset["width"])
            self.poster_height.set(preset["height"])
            self.photo_size.set(preset["photo_size"])
            self.students_per_row.set(preset["students_per_row"])
            self.margin.set(preset["margin"])

    def save_config(self):
        """Sauvegarde la configuration."""
        # Ici, on pourrait sauvegarder dans un fichier de configuration
        messagebox.showinfo("Information",
                           "Configuration sauvegardée!\n\n"
                           "💡 Pour une personnalisation complète,\n"
                           "modifiez le fichier config.py")
        self.window.destroy()

    def reset_config(self):
        """Réinitialise la configuration."""
        self.poster_width.set(1920)
        self.poster_height.set(1080)
        self.photo_size.set(120)
        self.students_per_row.set(4)
        self.margin.set(50)


def main():
    """Fonction principale."""
    try:
        app = InterfaceGenerateurAffiches()
        app.run()
    except Exception as e:
        import traceback
        error_msg = f"Erreur lors du lancement de l'interface:\n{str(e)}\n\n{traceback.format_exc()}"

        # Essayer d'afficher l'erreur dans une messagebox
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("Erreur", error_msg)
        except:
            print(error_msg)


if __name__ == "__main__":
    main()
