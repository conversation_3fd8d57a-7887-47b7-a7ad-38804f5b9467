# 🚀 Guide de Démarrage Rapide

## Première utilisation

1. **Générer une affiche d'exemple** :
   ```bash
   python congratulations_poster.py
   ```

2. **Utiliser vos propres données** :
   - Placez vos photos dans le dossier `photos/`
   - <PERSON><PERSON>ez votre fichier CSV dans le dossier `data/`
   - Modifiez le script ou utilisez l'exemple avancé

3. **Personnaliser l'apparence** :
   - Modi<PERSON>z le fichier `config.py`
   - Changez les couleurs, polices, dimensions, etc.

## Exemples de commandes

```python
# Utilisation basique
from congratulations_poster import CongratulationsPoster

generator = CongratulationsPoster('templates/fond_defaut.png')
generator.generate_poster('data/ma_classe.csv', 'Ma Classe')
```

```bash
# Exemple avancé avec plusieurs classes
python exemple_utilisation.py
```

```bash
# Tests
python test_poster_generator.py
```

## Structure des fichiers

- `data/` : Fichiers CSV avec les données des élèves
- `photos/` : Photos des élèves (JPG, PNG)
- `templates/` : Images de fond pour les affiches
- `output/` : Affiches générées
- `config.py` : Configuration personnalisable

## Format du CSV

| Nom | Note | Classement | Mention | Photo |
|-----|------|------------|---------|-------|
| Alice Martin | 18.5 | 1 | Très Bien | photos/alice.jpg |

## Aide

- Consultez le `README.md` pour plus de détails
- Modifiez `config.py` pour personnaliser
- Utilisez `test_poster_generator.py` pour vérifier le fonctionnement
