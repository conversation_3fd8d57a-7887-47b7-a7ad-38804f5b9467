#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configuration pour le générateur d'affiches de félicitations
===========================================================

Ce fichier contient tous les paramètres configurables du générateur.
Modifiez ces valeurs selon vos besoins.
"""

# Configuration des dimensions de l'affiche
POSTER_CONFIG = {
    'width': 1920,          # Largeur de l'affiche en pixels
    'height': 1080,         # Hauteur de l'affiche en pixels
    'photo_size': 120,      # Taille des photos d'élèves en pixels
    'margin': 50,           # Marge autour de l'affiche
    'students_per_row': 4,  # Nombre d'élèves par ligne
}

# Configuration des couleurs
COLORS = {
    'text_primary': '#2C3E50',      # Couleur principale du texte (bleu foncé)
    'text_secondary': '#7F8C8D',    # Couleur secondaire du texte (gris)
    'gold': '#F1C40F',              # Couleur pour "Très Bien" (or)
    'silver': '#BDC3C7',            # Couleur pour "Bien" (argent)
    'bronze': '#CD7F32',            # Couleur pour "Assez Bien" (bronze)
    'background': '#F8F9FA',        # Couleur de fond par défaut
    'border': '#4682B4',            # Couleur des bordures
}

# Configuration des polices
FONTS = {
    'title_size': 48,       # Taille de la police du titre
    'name_size': 20,        # Taille de la police des noms
    'info_size': 16,        # Taille de la police des informations
    'title_font': 'arial.ttf',      # Police du titre
    'text_font': 'arial.ttf',       # Police du texte
}

# Configuration des chemins
PATHS = {
    'data_dir': 'data',             # Dossier des fichiers CSV
    'photos_dir': 'photos',         # Dossier des photos d'élèves
    'templates_dir': 'templates',   # Dossier des templates d'arrière-plan
    'output_dir': 'output',         # Dossier de sortie des affiches
    'decorations_dir': 'decorations', # Dossier des éléments décoratifs
}

# Configuration des mentions et leurs couleurs
MENTIONS = {
    'Très Bien': {
        'color': COLORS['gold'],
        'emoji': '🥇',
        'description': 'Excellence'
    },
    'Bien': {
        'color': COLORS['silver'],
        'emoji': '🥈',
        'description': 'Très bon travail'
    },
    'Assez Bien': {
        'color': COLORS['bronze'],
        'emoji': '🥉',
        'description': 'Bon travail'
    },
    'Passable': {
        'color': COLORS['text_secondary'],
        'emoji': '📝',
        'description': 'Travail satisfaisant'
    }
}

# Configuration des décorations
DECORATIONS = {
    'graduation_cap': {
        'enabled': True,
        'size': 20,
        'color': COLORS['text_primary'],
        'position': 'top_right'  # Position relative à la photo
    },
    'stars': {
        'enabled': False,
        'count': 3,
        'size': 15,
        'color': COLORS['gold']
    },
    'border_style': {
        'enabled': True,
        'width': 20,
        'color': COLORS['border'],
        'style': 'solid'  # solid, dashed, dotted
    }
}

# Configuration du titre
TITLE_CONFIG = {
    'template': "🎓 TABLEAU D'HONNEUR - {class_name} 🎓",
    'position': 'center',   # center, left, right
    'y_offset': 50,         # Distance du haut de l'affiche
    'shadow': {
        'enabled': True,
        'offset_x': 2,
        'offset_y': 2,
        'color': 'gray'
    }
}

# Configuration de la mise en page
LAYOUT_CONFIG = {
    'title_area_height': 200,      # Hauteur réservée au titre
    'student_spacing_x': None,     # Espacement horizontal (None = automatique)
    'student_spacing_y': None,     # Espacement vertical (None = automatique)
    'text_offset_y': 10,           # Distance entre la photo et le texte
    'line_spacing': 25,            # Espacement entre les lignes de texte
}

# Configuration des formats de sortie
OUTPUT_CONFIG = {
    'format': 'PNG',               # Format de sortie (PNG, JPEG, BMP)
    'quality': 95,                 # Qualité pour JPEG (1-100)
    'dpi': 300,                    # Résolution en DPI
    'filename_template': 'affiche_{class_name}_{timestamp}',  # Template du nom de fichier
    'include_timestamp': False,     # Inclure un timestamp dans le nom
}

# Configuration des templates par défaut
DEFAULT_TEMPLATES = {
    'primary': {
        'background_color': (240, 248, 255),  # Bleu très clair
        'gradient': True,
        'gradient_colors': [(240, 248, 255), (220, 235, 255)],
        'border': True,
        'decorative_elements': True
    },
    'elegant': {
        'background_color': (248, 248, 255),  # Blanc cassé
        'gradient': False,
        'border': True,
        'decorative_elements': False
    },
    'colorful': {
        'background_color': (255, 250, 240),  # Beige clair
        'gradient': True,
        'gradient_colors': [(255, 250, 240), (255, 245, 220)],
        'border': True,
        'decorative_elements': True
    }
}

# Configuration des validations
VALIDATION = {
    'required_columns': ['Nom', 'Note', 'Classement', 'Mention', 'Photo'],
    'max_students_per_poster': 50,     # Limite d'élèves par affiche
    'min_note': 0,                     # Note minimale acceptée
    'max_note': 20,                    # Note maximale acceptée
    'valid_mentions': list(MENTIONS.keys()),  # Mentions valides
}

# Configuration du logging
LOGGING = {
    'level': 'INFO',               # DEBUG, INFO, WARNING, ERROR
    'log_to_file': False,          # Enregistrer dans un fichier
    'log_file': 'poster_generator.log',
    'format': '%(asctime)s - %(levelname)s - %(message)s'
}


def get_config():
    """
    Retourne la configuration complète sous forme de dictionnaire.
    
    Returns:
        dict: Configuration complète
    """
    return {
        'poster': POSTER_CONFIG,
        'colors': COLORS,
        'fonts': FONTS,
        'paths': PATHS,
        'mentions': MENTIONS,
        'decorations': DECORATIONS,
        'title': TITLE_CONFIG,
        'layout': LAYOUT_CONFIG,
        'output': OUTPUT_CONFIG,
        'templates': DEFAULT_TEMPLATES,
        'validation': VALIDATION,
        'logging': LOGGING
    }


def validate_config():
    """
    Valide la configuration et affiche les erreurs éventuelles.
    
    Returns:
        bool: True si la configuration est valide
    """
    errors = []
    
    # Vérifier les dimensions
    if POSTER_CONFIG['width'] <= 0 or POSTER_CONFIG['height'] <= 0:
        errors.append("Les dimensions de l'affiche doivent être positives")
    
    if POSTER_CONFIG['photo_size'] <= 0:
        errors.append("La taille des photos doit être positive")
    
    # Vérifier les couleurs (format hexadécimal)
    import re
    color_pattern = re.compile(r'^#[0-9A-Fa-f]{6}$')
    for color_name, color_value in COLORS.items():
        if not color_pattern.match(color_value):
            errors.append(f"Couleur invalide pour {color_name}: {color_value}")
    
    # Vérifier les mentions
    if not MENTIONS:
        errors.append("Au moins une mention doit être définie")
    
    # Afficher les erreurs
    if errors:
        print("❌ Erreurs de configuration détectées:")
        for error in errors:
            print(f"   - {error}")
        return False
    
    print("✅ Configuration valide")
    return True


if __name__ == "__main__":
    print("Configuration du générateur d'affiches")
    print("=" * 40)
    validate_config()
    
    config = get_config()
    print(f"\n📊 Résumé de la configuration:")
    print(f"   - Dimensions: {config['poster']['width']}x{config['poster']['height']}")
    print(f"   - Taille des photos: {config['poster']['photo_size']}px")
    print(f"   - Élèves par ligne: {config['poster']['students_per_row']}")
    print(f"   - Mentions définies: {len(config['mentions'])}")
    print(f"   - Format de sortie: {config['output']['format']}")
